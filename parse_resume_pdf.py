#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MinerU PDF 简历解析工具 - 完整功能版

文件描述：
    这是一个功能完整的 PDF 简历解析工具，使用 MinerU 框架将 PDF 文档转换为 Markdown 和 JSON 格式。
    提供多种解析方式和详细的结果分析功能。

主要功能：
    1. PDF 文档智能解析：支持文本、表格、公式、图片的识别和提取
    2. 多种解析方式：优先使用 Python API，失败时自动切换到命令行方式
    3. 跨平台支持：自动适配不同操作系统的路径和配置
    4. 结果分析：自动分析输出文件，提供内容预览和统计信息
    5. 错误处理：完善的异常处理机制，确保程序稳定运行

技术特点：
    - 使用 MinerU 的 pipeline 后端进行高质量解析
    - 支持中文 OCR 识别，适合中文简历处理
    - 启用公式和表格识别功能
    - 生成多种格式的输出文件（Markdown、JSON、图片）

输出格式：
    - Markdown 文件：保持文档结构的可读格式
    - JSON 文件：结构化数据，便于程序处理
    - 图片文件：从 PDF 中提取的图片资源
    - 分析报告：解析结果的详细统计信息

解析配置：
    - 语言：中文 (ch)
    - 后端：pipeline
    - 公式识别：启用
    - 表格识别：启用

目标文件：C:\Users\<USER>\Desktop\AI简历\有项目经验.pdf
输出目录：resume_output/

使用场景：
    - 简历内容提取和分析
    - PDF 文档数字化处理
    - 招聘系统的简历解析
    - 文档内容结构化存储

作者：文档处理团队
创建时间：2025年
版本：1.0
"""

import os
import sys
import json
from pathlib import Path
from datetime import datetime

def parse_pdf_with_mineru(pdf_path, output_dir=None, language="ch", backend="pipeline"):
    """
    使用 MinerU 解析 PDF 文件
    
    Args:
        pdf_path (str): PDF 文件路径
        output_dir (str): 输出目录，默认为当前目录下的 output
        language (str): OCR 语言，默认为中文 "ch"
        backend (str): 后端类型，默认为 "pipeline"
    
    Returns:
        dict: 解析结果信息
    """
    
    # 检查 PDF 文件是否存在
    if not os.path.exists(pdf_path):
        raise FileNotFoundError(f"PDF 文件不存在: {pdf_path}")
    
    # 设置输出目录
    if output_dir is None:
        output_dir = os.path.join(os.getcwd(), "resume_output")
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    print(f"开始解析 PDF: {pdf_path}")
    print(f"输出目录: {output_dir}")
    print(f"语言设置: {language}")
    print(f"后端类型: {backend}")
    print("-" * 50)
    
    try:
        # 导入 MinerU 相关模块
        from mineru.backend.pipeline.pipeline_analyze import doc_analyze
        from mineru.backend.pipeline.model_init import DocAnalysis
        from mineru.cli.common import _process_output
        
        # 初始化模型
        print("正在初始化 MinerU 模型...")
        doc_analysis = DocAnalysis()
        
        # 解析 PDF
        print("正在解析 PDF 文件...")
        result = doc_analyze(
            pdf_path=pdf_path,
            output_dir=output_dir,
            lang=language,
            backend=backend,
            formula=True,  # 启用公式识别
            table=True,    # 启用表格识别
        )
        
        print("PDF 解析完成！")
        return result
        
    except ImportError as e:
        print(f"导入 MinerU 模块失败: {e}")
        print("尝试使用命令行方式...")
        return parse_with_cli(pdf_path, output_dir, language, backend)
    
    except Exception as e:
        print(f"解析过程中出现错误: {e}")
        print("尝试使用命令行方式...")
        return parse_with_cli(pdf_path, output_dir, language, backend)


def parse_with_cli(pdf_path, output_dir, language="ch", backend="pipeline"):
    """
    使用命令行方式调用 MinerU
    
    Args:
        pdf_path (str): PDF 文件路径
        output_dir (str): 输出目录
        language (str): OCR 语言
        backend (str): 后端类型
    
    Returns:
        dict: 解析结果信息
    """
    import subprocess
    
    # 构建命令
    cmd = [
        sys.executable, "-m", "mineru.cli.client",
        "-p", pdf_path,
        "-o", output_dir,
        "-l", language,
        "-b", backend,
        "-f", "true",  # 启用公式识别
        "-t", "true",  # 启用表格识别
    ]
    
    print("执行命令:", " ".join(cmd))
    
    try:
        # 执行命令
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        
        print("命令执行成功！")
        print("标准输出:", result.stdout)
        
        if result.stderr:
            print("警告信息:", result.stderr)
        
        return {
            "success": True,
            "stdout": result.stdout,
            "stderr": result.stderr,
            "output_dir": output_dir
        }
        
    except subprocess.CalledProcessError as e:
        print(f"命令执行失败: {e}")
        print("错误输出:", e.stderr)
        return {
            "success": False,
            "error": str(e),
            "stderr": e.stderr
        }


def analyze_output(output_dir):
    """
    分析输出结果
    
    Args:
        output_dir (str): 输出目录
    
    Returns:
        dict: 分析结果
    """
    
    print("\n" + "=" * 50)
    print("分析输出结果")
    print("=" * 50)
    
    # 查找输出文件
    output_files = []
    for root, dirs, files in os.walk(output_dir):
        for file in files:
            file_path = os.path.join(root, file)
            output_files.append(file_path)
    
    print(f"找到 {len(output_files)} 个输出文件:")
    
    result = {
        "markdown_files": [],
        "json_files": [],
        "image_files": [],
        "other_files": []
    }
    
    for file_path in output_files:
        file_name = os.path.basename(file_path)
        file_size = os.path.getsize(file_path)
        
        print(f"  - {file_name} ({file_size} bytes)")
        
        if file_path.endswith('.md'):
            result["markdown_files"].append(file_path)
        elif file_path.endswith('.json'):
            result["json_files"].append(file_path)
        elif file_path.endswith(('.jpg', '.jpeg', '.png', '.gif')):
            result["image_files"].append(file_path)
        else:
            result["other_files"].append(file_path)
    
    # 读取 Markdown 内容
    if result["markdown_files"]:
        md_file = result["markdown_files"][0]
        print(f"\nMarkdown 文件内容预览 ({md_file}):")
        print("-" * 30)
        
        try:
            with open(md_file, 'r', encoding='utf-8') as f:
                content = f.read()
                # 显示前 500 个字符
                preview = content[:500]
                print(preview)
                if len(content) > 500:
                    print("...")
                    print(f"(总共 {len(content)} 个字符)")
        except Exception as e:
            print(f"读取 Markdown 文件失败: {e}")
    
    # 读取 JSON 内容
    if result["json_files"]:
        for json_file in result["json_files"]:
            if "content_list" in json_file:
                print(f"\nJSON 内容结构 ({json_file}):")
                print("-" * 30)
                
                try:
                    with open(json_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        print(f"内容块数量: {len(data)}")
                        
                        # 显示前几个内容块
                        for i, item in enumerate(data[:3]):
                            print(f"  块 {i+1}: {item.get('type', 'unknown')} - {item.get('text', '')[:50]}...")
                        
                        if len(data) > 3:
                            print(f"  ... 还有 {len(data) - 3} 个内容块")
                            
                except Exception as e:
                    print(f"读取 JSON 文件失败: {e}")
    
    return result


def main():
    """主函数"""
    
    # PDF 文件路径
    pdf_path = r"C:\Users\<USER>\Desktop\AI简历\有项目经验.pdf"
    
    # 输出目录
    output_dir = "resume_output"
    
    print("MinerU PDF 解析脚本")
    print("=" * 50)
    print(f"目标文件: {pdf_path}")
    print(f"输出目录: {output_dir}")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        # 解析 PDF
        result = parse_pdf_with_mineru(
            pdf_path=pdf_path,
            output_dir=output_dir,
            language="ch",  # 中文
            backend="pipeline"
        )
        
        # 分析输出结果
        analysis = analyze_output(output_dir)
        
        print("\n" + "=" * 50)
        print("解析完成！")
        print("=" * 50)
        print(f"输出目录: {os.path.abspath(output_dir)}")
        print(f"Markdown 文件: {len(analysis['markdown_files'])} 个")
        print(f"JSON 文件: {len(analysis['json_files'])} 个")
        print(f"图片文件: {len(analysis['image_files'])} 个")
        print(f"其他文件: {len(analysis['other_files'])} 个")
        
        # 保存分析结果
        analysis_file = os.path.join(output_dir, "analysis_result.json")
        with open(analysis_file, 'w', encoding='utf-8') as f:
            json.dump({
                "parse_result": result,
                "file_analysis": analysis,
                "timestamp": datetime.now().isoformat()
            }, f, ensure_ascii=False, indent=2)
        
        print(f"分析结果已保存到: {analysis_file}")
        
    except Exception as e:
        print(f"解析失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
