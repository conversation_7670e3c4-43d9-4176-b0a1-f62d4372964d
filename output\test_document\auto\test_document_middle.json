{"pdf_info": [{"preproc_blocks": [{"type": "title", "bbox": [99, 85, 265, 103], "lines": [{"bbox": [97, 83, 267, 105], "spans": [{"bbox": [97, 83, 267, 105], "score": 1.0, "content": "MinerU Test Document", "type": "text"}], "index": 0}], "index": 0}, {"type": "text", "bbox": [98, 137, 419, 173], "lines": [{"bbox": [97, 136, 420, 155], "spans": [{"bbox": [97, 136, 420, 155], "score": 1.0, "content": "This is a test document for MinerU PDF parsing capabilities.", "type": "text"}], "index": 1}, {"bbox": [97, 157, 340, 174], "spans": [{"bbox": [97, 157, 340, 174], "score": 1.0, "content": "It contains various types of content including:", "type": "text"}], "index": 2}], "index": 1.5}, {"type": "text", "bbox": [98, 178, 251, 253], "lines": [{"bbox": [97, 177, 237, 195], "spans": [{"bbox": [97, 177, 237, 195], "score": 1.0, "content": "- Regular text paragraphs", "type": "text"}], "index": 3}, {"bbox": [98, 198, 229, 213], "spans": [{"bbox": [98, 198, 229, 213], "score": 1.0, "content": "- Mathematical formulas", "type": "text"}], "index": 4}, {"bbox": [97, 218, 251, 234], "spans": [{"bbox": [97, 218, 251, 234], "score": 1.0, "content": "- Tables and structured data", "type": "text"}], "index": 5}, {"bbox": [97, 237, 237, 255], "spans": [{"bbox": [97, 237, 237, 255], "score": 1.0, "content": "- Mixed language content", "type": "text"}], "index": 6}], "index": 4.5}, {"type": "text", "bbox": [97, 278, 273, 335], "lines": [{"bbox": [97, 277, 273, 294], "spans": [{"bbox": [97, 277, 273, 294], "score": 1.0, "content": "Mathematical Formula Example:", "type": "text"}], "index": 7}, {"bbox": [97, 297, 196, 335], "spans": [{"bbox": [97, 297, 196, 335], "score": 0.35, "content": "\\begin{array} { l } { { \\mathsf E } = { \\mathsf m } { \\mathsf c } ^ { 2 } } \\\\ { \\int { \\mathsf f } ( { \\mathsf x } ) \\mathrm { d } { \\mathsf x } = { \\mathsf F } ( { \\mathsf x } ) + { \\mathsf C } } \\end{array}", "type": "inline_equation", "image_path": "37534bb155edcd2079c88ab8ae1fea0c647c53347ec84c9ac524c54869560ddc.jpg"}], "index": 8}], "index": 7.5}, {"type": "text", "bbox": [99, 358, 184, 372], "lines": [{"bbox": [97, 357, 185, 375], "spans": [{"bbox": [97, 357, 185, 375], "score": 1.0, "content": "Table Example:", "type": "text"}], "index": 9}], "index": 9}, {"type": "text", "bbox": [98, 378, 248, 453], "lines": [{"bbox": [96, 376, 217, 397], "spans": [{"bbox": [96, 377, 134, 395], "score": 1.0, "content": "Name", "type": "text"}, {"bbox": [154, 376, 183, 397], "score": 1.0, "content": "Age", "type": "text"}, {"bbox": [188, 376, 217, 397], "score": 1.0, "content": "City", "type": "text"}], "index": 10}, {"bbox": [97, 396, 218, 418], "spans": [{"bbox": [97, 397, 129, 416], "score": 1.0, "content": "<PERSON>", "type": "text"}, {"bbox": [146, 397, 166, 416], "score": 1.0, "content": "25", "type": "text"}, {"bbox": [176, 396, 218, 418], "score": 1.0, "content": "Beijing", "type": "text"}], "index": 11}, {"bbox": [97, 416, 235, 437], "spans": [{"bbox": [97, 416, 124, 435], "score": 1.0, "content": "<PERSON>", "type": "text"}, {"bbox": [147, 416, 168, 435], "score": 1.0, "content": "30", "type": "text"}, {"bbox": [177, 417, 235, 437], "score": 1.0, "content": "Shanghai", "type": "text"}], "index": 12}, {"bbox": [97, 437, 249, 457], "spans": [{"bbox": [97, 437, 141, 455], "score": 1.0, "content": "<PERSON>", "type": "text"}, {"bbox": [151, 437, 171, 455], "score": 1.0, "content": "28", "type": "text"}, {"bbox": [181, 437, 249, 457], "score": 1.0, "content": "Guangzhou", "type": "text"}], "index": 13}], "index": 11.5}, {"type": "text", "bbox": [98, 478, 402, 513], "lines": [{"bbox": [99, 478, 401, 493], "spans": [{"bbox": [99, 478, 401, 493], "score": 1.0, "content": "This document tests the extraction capabilities of MinerU", "type": "text"}], "index": 14}, {"bbox": [97, 498, 357, 514], "spans": [{"bbox": [97, 498, 357, 514], "score": 1.0, "content": "for converting PDF content to Markdown format.", "type": "text"}], "index": 15}], "index": 14.5}], "page_idx": 0, "page_size": [612, 792], "discarded_blocks": [], "para_blocks": [{"type": "title", "bbox": [99, 85, 265, 103], "lines": [{"bbox": [97, 83, 267, 105], "spans": [{"bbox": [97, 83, 267, 105], "score": 1.0, "content": "MinerU Test Document", "type": "text"}], "index": 0}], "index": 0}, {"type": "text", "bbox": [98, 137, 419, 173], "lines": [{"bbox": [97, 136, 420, 155], "spans": [{"bbox": [97, 136, 420, 155], "score": 1.0, "content": "This is a test document for MinerU PDF parsing capabilities.", "type": "text"}], "index": 1}, {"bbox": [97, 157, 340, 174], "spans": [{"bbox": [97, 157, 340, 174], "score": 1.0, "content": "It contains various types of content including:", "type": "text"}], "index": 2}], "index": 1.5, "bbox_fs": [97, 136, 420, 174]}, {"type": "text", "bbox": [98, 178, 251, 253], "lines": [{"bbox": [97, 177, 237, 195], "spans": [{"bbox": [97, 177, 237, 195], "score": 1.0, "content": "- Regular text paragraphs", "type": "text"}], "index": 3}, {"bbox": [98, 198, 229, 213], "spans": [{"bbox": [98, 198, 229, 213], "score": 1.0, "content": "- Mathematical formulas", "type": "text"}], "index": 4}, {"bbox": [97, 218, 251, 234], "spans": [{"bbox": [97, 218, 251, 234], "score": 1.0, "content": "- Tables and structured data", "type": "text"}], "index": 5}, {"bbox": [97, 237, 237, 255], "spans": [{"bbox": [97, 237, 237, 255], "score": 1.0, "content": "- Mixed language content", "type": "text"}], "index": 6}], "index": 4.5, "bbox_fs": [97, 177, 251, 255]}, {"type": "text", "bbox": [97, 278, 273, 335], "lines": [{"bbox": [97, 277, 273, 294], "spans": [{"bbox": [97, 277, 273, 294], "score": 1.0, "content": "Mathematical Formula Example:", "type": "text"}], "index": 7}, {"bbox": [97, 297, 196, 335], "spans": [{"bbox": [97, 297, 196, 335], "score": 0.35, "content": "\\begin{array} { l } { { \\mathsf E } = { \\mathsf m } { \\mathsf c } ^ { 2 } } \\\\ { \\int { \\mathsf f } ( { \\mathsf x } ) \\mathrm { d } { \\mathsf x } = { \\mathsf F } ( { \\mathsf x } ) + { \\mathsf C } } \\end{array}", "type": "inline_equation", "image_path": "37534bb155edcd2079c88ab8ae1fea0c647c53347ec84c9ac524c54869560ddc.jpg"}], "index": 8}], "index": 7.5, "bbox_fs": [97, 277, 273, 335]}, {"type": "text", "bbox": [99, 358, 184, 372], "lines": [{"bbox": [97, 357, 185, 375], "spans": [{"bbox": [97, 357, 185, 375], "score": 1.0, "content": "Table Example:", "type": "text"}], "index": 9}], "index": 9, "bbox_fs": [97, 357, 185, 375]}, {"type": "text", "bbox": [98, 378, 248, 453], "lines": [{"bbox": [96, 376, 217, 397], "spans": [{"bbox": [96, 377, 134, 395], "score": 1.0, "content": "Name", "type": "text"}, {"bbox": [154, 376, 183, 397], "score": 1.0, "content": "Age", "type": "text"}, {"bbox": [188, 376, 217, 397], "score": 1.0, "content": "City", "type": "text"}], "index": 10}, {"bbox": [97, 396, 218, 418], "spans": [{"bbox": [97, 397, 129, 416], "score": 1.0, "content": "<PERSON>", "type": "text"}, {"bbox": [146, 397, 166, 416], "score": 1.0, "content": "25", "type": "text"}, {"bbox": [176, 396, 218, 418], "score": 1.0, "content": "Beijing", "type": "text"}], "index": 11}, {"bbox": [97, 416, 235, 437], "spans": [{"bbox": [97, 416, 124, 435], "score": 1.0, "content": "<PERSON>", "type": "text"}, {"bbox": [147, 416, 168, 435], "score": 1.0, "content": "30", "type": "text"}, {"bbox": [177, 417, 235, 437], "score": 1.0, "content": "Shanghai", "type": "text"}], "index": 12}, {"bbox": [97, 437, 249, 457], "spans": [{"bbox": [97, 437, 141, 455], "score": 1.0, "content": "<PERSON>", "type": "text"}, {"bbox": [151, 437, 171, 455], "score": 1.0, "content": "28", "type": "text"}, {"bbox": [181, 437, 249, 457], "score": 1.0, "content": "Guangzhou", "type": "text"}], "index": 13}], "index": 11.5, "bbox_fs": [96, 376, 249, 457]}, {"type": "text", "bbox": [98, 478, 402, 513], "lines": [{"bbox": [99, 478, 401, 493], "spans": [{"bbox": [99, 478, 401, 493], "score": 1.0, "content": "This document tests the extraction capabilities of MinerU", "type": "text"}], "index": 14}, {"bbox": [97, 498, 357, 514], "spans": [{"bbox": [97, 498, 357, 514], "score": 1.0, "content": "for converting PDF content to Markdown format.", "type": "text"}], "index": 15}], "index": 14.5, "bbox_fs": [97, 478, 401, 514]}]}], "_backend": "pipeline", "_version_name": "2.1.4"}