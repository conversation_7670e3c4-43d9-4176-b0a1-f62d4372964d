#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VSCode 清理工具

文件描述：
    这是一个用于清理 VSCode 相关数据和激活 Cursor 编辑器的自动化脚本。
    主要功能包括：
    1. 运行 augmentClear.py 清理 VSCode 数据库中的 Augment 相关记录
    2. 以管理员权限启动 CursorPro.exe 激活工具
    3. 自动化输入激活参数（使用 pyautogui 模拟键盘输入）
    4. 删除 VSCode 的用户配置目录和数据目录

主要功能：
    - 自动检测管理员权限，如无权限则重新以管理员身份运行
    - 集成多个清理步骤，实现一键式 VSCode 环境重置
    - 支持自动化激活流程，减少手动操作

使用场景：
    - VSCode 环境出现问题需要完全重置
    - 需要清理 Augment 插件相关数据
    - 批量部署或维护开发环境

注意事项：
    - 需要管理员权限运行
    - 会删除 VSCode 的所有用户配置和扩展
    - 需要安装 pyautogui 库用于自动化输入
    - CursorPro.exe 路径需要根据实际情况调整

作者：系统管理员
创建时间：2025年
版本：1.0
"""

import subprocess
import os
import shutil
import time
import ctypes
import sys

# 检查是否以管理员权限运行
def is_admin():
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

# 删除文件夹
def delete_dir(path):
    if os.path.exists(path):
        print(f"正在删除：{path}")
        shutil.rmtree(path)
        print(f"已删除：{path}")
    else:
        print(f"路径不存在：{path}")

# 主程序入口
def main():
    # 1. 运行 augmentClear.py
    augment_script = r"D:\code\demo\augmentClear.py"
    print("运行 augmentClear.py ...")
    subprocess.run(["python", augment_script], check=True)

    # 2. 以管理员身份运行 CursorPro.exe（等待密码输入完成）
    cursor_pro_exe = r"D:\code\cuursor激活\CursorPro-Windows\CursorPro-Windows\CursorPro.exe"
    print("以管理员身份运行 CursorPro.exe ...")

    params = f'Start-Process -FilePath "{cursor_pro_exe}" -Verb runAs'
    subprocess.run(["powershell", "-Command", params], shell=True)
    print("等待程序启动...")
    time.sleep(5)  # 等待程序响应

    # 3. 模拟输入 1 和 y
    try:
        import pyautogui
        print("模拟输入 1 和 y...")
        pyautogui.typewrite('1\n', interval=0.1)
        time.sleep(1)
        pyautogui.typewrite('1\n', interval=0.1)
        time.sleep(2)
        pyautogui.typewrite('y\n', interval=0.1)
    except ImportError:
        print("未安装 pyautogui，无法模拟输入")

    # 4. 删除 VSCode 相关目录
    delete_dir(r"C:\Users\<USER>\.vscode")
    delete_dir(r"C:\Users\<USER>\AppData\Roaming\code")

    print("全部操作完成。")

# 脚本启动点
if __name__ == "__main__":
    if not is_admin():
        print("请以管理员权限重新运行该脚本！")
        # 重新以管理员权限运行脚本
        ctypes.windll.shell32.ShellExecuteW(
            None, "runas", sys.executable, __file__, None, 1)
        sys.exit()

    main()
