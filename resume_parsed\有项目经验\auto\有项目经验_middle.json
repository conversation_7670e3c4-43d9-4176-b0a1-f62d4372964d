{"pdf_info": [{"preproc_blocks": [{"type": "title", "bbox": [46, 54, 89, 76], "lines": [{"bbox": [43, 52, 91, 80], "spans": [{"bbox": [43, 52, 91, 80], "score": 1.0, "content": "张三", "type": "text"}], "index": 0}], "index": 0}, {"type": "text", "bbox": [45, 86, 203, 155], "lines": [{"bbox": [46, 87, 129, 104], "spans": [{"bbox": [46, 87, 129, 104], "score": 1.0, "content": "高级项目经理", "type": "text"}], "index": 1}, {"bbox": [46, 119, 151, 133], "spans": [{"bbox": [46, 119, 151, 133], "score": 0.999, "content": "手机：138-1234-5678", "type": "text"}], "index": 2}, {"bbox": [45, 138, 201, 155], "spans": [{"bbox": [45, 138, 201, 155], "score": 1.0, "content": "邮箱：<EMAIL>", "type": "text"}], "index": 3}], "index": 2}, {"type": "title", "bbox": [51, 192, 108, 209], "lines": [{"bbox": [50, 192, 109, 211], "spans": [{"bbox": [50, 192, 109, 211], "score": 1.0, "content": "基本信息", "type": "text"}], "index": 4}], "index": 4}, {"type": "text", "bbox": [46, 228, 474, 263], "lines": [{"bbox": [44, 228, 473, 246], "spans": [{"bbox": [44, 229, 70, 246], "score": 0.996, "content": "生日", "type": "text"}, {"bbox": [172, 229, 198, 245], "score": 1.0, "content": "籍贯", "type": "text"}, {"bbox": [299, 228, 335, 246], "score": 1.0, "content": "现居地", "type": "text"}, {"bbox": [427, 230, 473, 245], "score": 1.0, "content": "工作年限", "type": "text"}], "index": 5}, {"bbox": [45, 248, 448, 264], "spans": [{"bbox": [45, 249, 120, 263], "score": 1.0, "content": "1990年5月15日", "type": "text"}, {"bbox": [172, 248, 218, 263], "score": 1.0, "content": "江苏南京", "type": "text"}, {"bbox": [299, 248, 325, 264], "score": 1.0, "content": "上海", "type": "text"}, {"bbox": [425, 248, 448, 264], "score": 1.0, "content": "7年", "type": "text"}], "index": 6}], "index": 5.5}, {"type": "title", "bbox": [52, 285, 108, 302], "lines": [{"bbox": [50, 284, 110, 304], "spans": [{"bbox": [50, 284, 110, 304], "score": 1.0, "content": "个人荣誉", "type": "text"}], "index": 7}], "index": 7}, {"type": "text", "bbox": [49, 322, 174, 388], "lines": [{"bbox": [48, 322, 162, 343], "spans": [{"bbox": [48, 322, 162, 343], "score": 0.902, "content": "• 2020年度优秀员工", "type": "text"}], "index": 8}, {"bbox": [49, 347, 162, 366], "spans": [{"bbox": [49, 347, 162, 366], "score": 0.928, "content": "● 2019年创新项目奖", "type": "text"}], "index": 9}, {"bbox": [48, 371, 174, 392], "spans": [{"bbox": [48, 371, 174, 392], "score": 0.915, "content": "• 2018年行业最佳新人", "type": "text"}], "index": 10}], "index": 9}, {"type": "title", "bbox": [51, 411, 107, 428], "lines": [{"bbox": [50, 411, 109, 430], "spans": [{"bbox": [50, 411, 109, 430], "score": 1.0, "content": "教育背景", "type": "text"}], "index": 11}], "index": 11}, {"type": "title", "bbox": [46, 449, 96, 464], "lines": [{"bbox": [45, 448, 97, 466], "spans": [{"bbox": [45, 448, 97, 466], "score": 1.0, "content": "清华大学", "type": "text"}], "index": 12}], "index": 12}, {"type": "text", "bbox": [450, 449, 550, 465], "lines": [{"bbox": [450, 449, 551, 466], "spans": [{"bbox": [450, 449, 551, 466], "score": 0.993, "content": "2010.09 -2014.06", "type": "text"}], "index": 13}], "index": 13}, {"type": "title", "bbox": [47, 472, 178, 488], "lines": [{"bbox": [46, 473, 178, 487], "spans": [{"bbox": [46, 473, 178, 487], "score": 0.964, "content": "计算机科学与技术／本科", "type": "text"}], "index": 14}], "index": 14}, {"type": "text", "bbox": [51, 497, 331, 556], "lines": [{"bbox": [50, 499, 331, 514], "spans": [{"bbox": [50, 499, 331, 514], "score": 0.961, "content": "• 主修课程：数据结构、算法设计、数据库原理、计算机网络", "type": "text"}], "index": 15}, {"bbox": [51, 521, 178, 536], "spans": [{"bbox": [51, 521, 161, 536], "score": 0.959, "content": "•GPA:3.8/4.0，排名前", "type": "text"}, {"bbox": [162, 521, 178, 533], "score": 0.82, "content": "5 \\%", "type": "inline_equation"}], "index": 16}, {"bbox": [50, 542, 174, 558], "spans": [{"bbox": [50, 542, 174, 558], "score": 0.918, "content": "· 获得校级一等奖学金2次", "type": "text"}], "index": 17}], "index": 16}, {"type": "title", "bbox": [47, 576, 120, 591], "lines": [{"bbox": [46, 576, 120, 592], "spans": [{"bbox": [46, 576, 120, 592], "score": 1.0, "content": "上海交通大学", "type": "text"}], "index": 18}], "index": 18}, {"type": "text", "bbox": [450, 576, 550, 591], "lines": [{"bbox": [450, 576, 550, 592], "spans": [{"bbox": [450, 576, 550, 592], "score": 0.999, "content": "2014.09-2017.06", "type": "text"}], "index": 19}], "index": 19}, {"type": "title", "bbox": [46, 599, 130, 614], "lines": [{"bbox": [45, 599, 130, 614], "spans": [{"bbox": [45, 599, 130, 614], "score": 0.937, "content": "软件工程/硕士", "type": "text"}], "index": 20}], "index": 20}, {"type": "text", "bbox": [51, 624, 210, 683], "lines": [{"bbox": [50, 625, 205, 640], "spans": [{"bbox": [50, 625, 205, 640], "score": 0.928, "content": "• 研究方向:人工智能与机器学习", "type": "text"}], "index": 21}, {"bbox": [50, 646, 210, 662], "spans": [{"bbox": [50, 646, 210, 662], "score": 0.939, "content": "· 发表论文2篇，其中1篇被EI收录", "type": "text"}], "index": 22}, {"bbox": [50, 668, 189, 684], "spans": [{"bbox": [50, 668, 189, 684], "score": 0.935, "content": "● 参与国家自然科学基金项目", "type": "text"}], "index": 23}], "index": 22}, {"type": "title", "bbox": [52, 706, 108, 723], "lines": [{"bbox": [51, 706, 109, 725], "spans": [{"bbox": [51, 706, 109, 725], "score": 1.0, "content": "工作经历", "type": "text"}], "index": 24}], "index": 24}, {"type": "title", "bbox": [45, 744, 120, 759], "lines": [{"bbox": [45, 744, 120, 761], "spans": [{"bbox": [45, 744, 120, 761], "score": 0.999, "content": "阿里巴巴集团", "type": "text"}], "index": 25}], "index": 25}], "page_idx": 0, "page_size": [595, 841], "discarded_blocks": [{"type": "discarded", "bbox": [470, 744, 549, 759], "lines": [{"bbox": [469, 744, 551, 760], "spans": [{"bbox": [469, 744, 551, 760], "score": 0.995, "content": "2019.07-至今", "type": "text"}]}]}, {"type": "discarded", "bbox": [0, 0, 115, 11], "lines": [{"bbox": [0, 0, 115, 10], "spans": [{"bbox": [0, 0, 115, 10], "score": 1.0, "content": "张三 - 高级项目经理简历", "type": "text"}]}]}, {"type": "discarded", "bbox": [486, 73, 549, 136], "lines": [{"bbox": [485, 71, 552, 140], "spans": [{"bbox": [485, 71, 552, 140], "score": 0.687, "content": "8", "type": "text"}]}]}, {"type": "discarded", "bbox": [484, 830, 595, 841], "lines": [{"bbox": [484, 830, 596, 842], "spans": [{"bbox": [484, 830, 596, 842], "score": 1.0, "content": "2025/7/23 星期三 16:43", "type": "text"}]}]}, {"type": "discarded", "bbox": [304, 0, 593, 11], "lines": [{"bbox": [304, -1, 595, 11], "spans": [{"bbox": [304, -1, 595, 11], "score": 1.0, "content": "file:///C:/Users/<USER>/Desktop/AI%E7%AE%80%E5%8...", "type": "text"}]}]}, {"type": "discarded", "bbox": [0, 829, 55, 841], "lines": [{"bbox": [-1, 830, 56, 843], "spans": [{"bbox": [-1, 830, 56, 843], "score": 1.0, "content": "第1页 共3页", "type": "text"}]}]}, {"type": "discarded", "bbox": [46, 767, 120, 782], "lines": [{"bbox": [46, 767, 120, 781], "spans": [{"bbox": [46, 767, 120, 781], "score": 1.0, "content": "高级项目经理", "type": "text"}]}]}], "para_blocks": [{"type": "title", "bbox": [46, 54, 89, 76], "lines": [{"bbox": [43, 52, 91, 80], "spans": [{"bbox": [43, 52, 91, 80], "score": 1.0, "content": "张三", "type": "text"}], "index": 0}], "index": 0}, {"type": "list", "bbox": [45, 86, 203, 155], "lines": [{"bbox": [46, 87, 129, 104], "spans": [{"bbox": [46, 87, 129, 104], "score": 1.0, "content": "高级项目经理", "type": "text"}], "index": 1, "is_list_start_line": true}, {"bbox": [46, 119, 151, 133], "spans": [{"bbox": [46, 119, 151, 133], "score": 0.999, "content": "手机：138-1234-5678", "type": "text"}], "index": 2, "is_list_start_line": true}, {"bbox": [45, 138, 201, 155], "spans": [{"bbox": [45, 138, 201, 155], "score": 1.0, "content": "邮箱：<EMAIL>", "type": "text"}], "index": 3, "is_list_start_line": true}], "index": 2, "bbox_fs": [45, 87, 201, 155]}, {"type": "title", "bbox": [51, 192, 108, 209], "lines": [{"bbox": [50, 192, 109, 211], "spans": [{"bbox": [50, 192, 109, 211], "score": 1.0, "content": "基本信息", "type": "text"}], "index": 4}], "index": 4}, {"type": "text", "bbox": [46, 228, 474, 263], "lines": [{"bbox": [44, 228, 473, 246], "spans": [{"bbox": [44, 229, 70, 246], "score": 0.996, "content": "生日", "type": "text"}, {"bbox": [172, 229, 198, 245], "score": 1.0, "content": "籍贯", "type": "text"}, {"bbox": [299, 228, 335, 246], "score": 1.0, "content": "现居地", "type": "text"}, {"bbox": [427, 230, 473, 245], "score": 1.0, "content": "工作年限", "type": "text"}], "index": 5}, {"bbox": [45, 248, 448, 264], "spans": [{"bbox": [45, 249, 120, 263], "score": 1.0, "content": "1990年5月15日", "type": "text"}, {"bbox": [172, 248, 218, 263], "score": 1.0, "content": "江苏南京", "type": "text"}, {"bbox": [299, 248, 325, 264], "score": 1.0, "content": "上海", "type": "text"}, {"bbox": [425, 248, 448, 264], "score": 1.0, "content": "7年", "type": "text"}], "index": 6}], "index": 5.5, "bbox_fs": [44, 228, 473, 264]}, {"type": "title", "bbox": [52, 285, 108, 302], "lines": [{"bbox": [50, 284, 110, 304], "spans": [{"bbox": [50, 284, 110, 304], "score": 1.0, "content": "个人荣誉", "type": "text"}], "index": 7}], "index": 7}, {"type": "text", "bbox": [49, 322, 174, 388], "lines": [{"bbox": [48, 322, 162, 343], "spans": [{"bbox": [48, 322, 162, 343], "score": 0.902, "content": "• 2020年度优秀员工", "type": "text"}], "index": 8}, {"bbox": [49, 347, 162, 366], "spans": [{"bbox": [49, 347, 162, 366], "score": 0.928, "content": "● 2019年创新项目奖", "type": "text"}], "index": 9}, {"bbox": [48, 371, 174, 392], "spans": [{"bbox": [48, 371, 174, 392], "score": 0.915, "content": "• 2018年行业最佳新人", "type": "text"}], "index": 10}], "index": 9, "bbox_fs": [48, 322, 174, 392]}, {"type": "title", "bbox": [51, 411, 107, 428], "lines": [{"bbox": [50, 411, 109, 430], "spans": [{"bbox": [50, 411, 109, 430], "score": 1.0, "content": "教育背景", "type": "text"}], "index": 11}], "index": 11}, {"type": "title", "bbox": [46, 449, 96, 464], "lines": [{"bbox": [45, 448, 97, 466], "spans": [{"bbox": [45, 448, 97, 466], "score": 1.0, "content": "清华大学", "type": "text"}], "index": 12}], "index": 12}, {"type": "text", "bbox": [450, 449, 550, 465], "lines": [{"bbox": [450, 449, 551, 466], "spans": [{"bbox": [450, 449, 551, 466], "score": 0.993, "content": "2010.09 -2014.06", "type": "text"}], "index": 13}], "index": 13, "bbox_fs": [450, 449, 551, 466]}, {"type": "title", "bbox": [47, 472, 178, 488], "lines": [{"bbox": [46, 473, 178, 487], "spans": [{"bbox": [46, 473, 178, 487], "score": 0.964, "content": "计算机科学与技术／本科", "type": "text"}], "index": 14}], "index": 14}, {"type": "list", "bbox": [51, 497, 331, 556], "lines": [{"bbox": [50, 499, 331, 514], "spans": [{"bbox": [50, 499, 331, 514], "score": 0.961, "content": "• 主修课程：数据结构、算法设计、数据库原理、计算机网络", "type": "text"}], "index": 15, "is_list_start_line": true}, {"bbox": [51, 521, 178, 536], "spans": [{"bbox": [51, 521, 161, 536], "score": 0.959, "content": "•GPA:3.8/4.0，排名前", "type": "text"}, {"bbox": [162, 521, 178, 533], "score": 0.82, "content": "5 \\%", "type": "inline_equation"}], "index": 16, "is_list_start_line": true}, {"bbox": [50, 542, 174, 558], "spans": [{"bbox": [50, 542, 174, 558], "score": 0.918, "content": "· 获得校级一等奖学金2次", "type": "text"}], "index": 17, "is_list_start_line": true}], "index": 16, "bbox_fs": [50, 499, 331, 558]}, {"type": "title", "bbox": [47, 576, 120, 591], "lines": [{"bbox": [46, 576, 120, 592], "spans": [{"bbox": [46, 576, 120, 592], "score": 1.0, "content": "上海交通大学", "type": "text"}], "index": 18}], "index": 18}, {"type": "text", "bbox": [450, 576, 550, 591], "lines": [{"bbox": [450, 576, 550, 592], "spans": [{"bbox": [450, 576, 550, 592], "score": 0.999, "content": "2014.09-2017.06", "type": "text"}], "index": 19}], "index": 19, "bbox_fs": [450, 576, 550, 592]}, {"type": "title", "bbox": [46, 599, 130, 614], "lines": [{"bbox": [45, 599, 130, 614], "spans": [{"bbox": [45, 599, 130, 614], "score": 0.937, "content": "软件工程/硕士", "type": "text"}], "index": 20}], "index": 20}, {"type": "text", "bbox": [51, 624, 210, 683], "lines": [{"bbox": [50, 625, 205, 640], "spans": [{"bbox": [50, 625, 205, 640], "score": 0.928, "content": "• 研究方向:人工智能与机器学习", "type": "text"}], "index": 21}, {"bbox": [50, 646, 210, 662], "spans": [{"bbox": [50, 646, 210, 662], "score": 0.939, "content": "· 发表论文2篇，其中1篇被EI收录", "type": "text"}], "index": 22}, {"bbox": [50, 668, 189, 684], "spans": [{"bbox": [50, 668, 189, 684], "score": 0.935, "content": "● 参与国家自然科学基金项目", "type": "text"}], "index": 23}], "index": 22, "bbox_fs": [50, 625, 210, 684]}, {"type": "title", "bbox": [52, 706, 108, 723], "lines": [{"bbox": [51, 706, 109, 725], "spans": [{"bbox": [51, 706, 109, 725], "score": 1.0, "content": "工作经历", "type": "text"}], "index": 24}], "index": 24}, {"type": "title", "bbox": [45, 744, 120, 759], "lines": [{"bbox": [45, 744, 120, 761], "spans": [{"bbox": [45, 744, 120, 761], "score": 0.999, "content": "阿里巴巴集团", "type": "text"}], "index": 25}], "index": 25}]}, {"preproc_blocks": [{"type": "text", "bbox": [50, 37, 426, 95], "lines": [{"bbox": [50, 38, 426, 52], "spans": [{"bbox": [50, 38, 426, 52], "score": 0.978, "content": "• 负责电商平台用户增长产品线，带领5人产品团队，通过数据驱动优化产品策略", "type": "text"}], "index": 0}, {"bbox": [50, 59, 357, 74], "spans": [{"bbox": [50, 59, 357, 74], "score": 0.974, "content": "• 协调研发、设计、运营等多部门资源，确保项目按时高质量交付", "type": "text"}], "index": 1}, {"bbox": [49, 81, 262, 96], "spans": [{"bbox": [49, 81, 239, 96], "score": 0.946, "content": "•建立了产品标准化流程，提高团队效率", "type": "text"}, {"bbox": [240, 81, 262, 94], "score": 0.82, "content": "2 0 \\%", "type": "inline_equation"}], "index": 2}], "index": 1}, {"type": "title", "bbox": [46, 114, 144, 130], "lines": [{"bbox": [46, 115, 143, 130], "spans": [{"bbox": [46, 115, 143, 130], "score": 0.997, "content": "腾讯科技有限公司", "type": "text"}], "index": 3}], "index": 3}, {"type": "text", "bbox": [451, 115, 550, 130], "lines": [{"bbox": [450, 115, 550, 131], "spans": [{"bbox": [450, 115, 550, 131], "score": 0.999, "content": "2017.07-2019.06", "type": "text"}], "index": 4}], "index": 4}, {"type": "title", "bbox": [46, 138, 96, 153], "lines": [{"bbox": [45, 138, 96, 153], "spans": [{"bbox": [45, 138, 96, 153], "score": 1.0, "content": "项目经理", "type": "text"}], "index": 5}], "index": 5}, {"type": "text", "bbox": [51, 163, 333, 222], "lines": [{"bbox": [50, 164, 333, 180], "spans": [{"bbox": [50, 164, 333, 180], "score": 0.965, "content": "· 负责微信小程序生态产品规划，主导B端工具类小程序开发", "type": "text"}], "index": 6}, {"bbox": [50, 186, 326, 202], "spans": [{"bbox": [50, 186, 326, 202], "score": 0.962, "content": "· 通过用户调研和数据分析，优化产品体验，NPS提升25分", "type": "text"}], "index": 7}, {"bbox": [50, 207, 294, 224], "spans": [{"bbox": [50, 207, 271, 224], "score": 0.973, "content": "·协助制定小程序商业化策略，实现年收入增长", "type": "text"}, {"bbox": [271, 208, 294, 220], "score": 0.82, "content": "5 0 \\%", "type": "inline_equation"}], "index": 8}], "index": 7}, {"type": "title", "bbox": [52, 245, 135, 262], "lines": [{"bbox": [51, 245, 136, 263], "spans": [{"bbox": [51, 245, 136, 263], "score": 1.0, "content": "重点项目经历", "type": "text"}], "index": 9}], "index": 9}, {"type": "title", "bbox": [60, 293, 182, 309], "lines": [{"bbox": [59, 294, 181, 310], "spans": [{"bbox": [59, 294, 181, 310], "score": 0.981, "content": "电商平台用户增长系统", "type": "text"}], "index": 10}], "index": 10}, {"type": "text", "bbox": [451, 293, 539, 308], "lines": [{"bbox": [451, 294, 539, 308], "spans": [{"bbox": [451, 294, 539, 308], "score": 0.999, "content": "2019.10-2020.06", "type": "text"}], "index": 11}], "index": 11}, {"type": "text", "bbox": [60, 320, 185, 334], "lines": [{"bbox": [59, 320, 184, 334], "spans": [{"bbox": [59, 320, 184, 334], "score": 0.994, "content": "项目负责人|阿里巴巴集团", "type": "text"}], "index": 12}], "index": 12}, {"type": "title", "bbox": [59, 345, 114, 360], "lines": [{"bbox": [58, 345, 117, 362], "spans": [{"bbox": [58, 345, 117, 362], "score": 0.944, "content": "项目背景：", "type": "text"}], "index": 13}], "index": 13}, {"type": "text", "bbox": [56, 367, 462, 384], "lines": [{"bbox": [58, 367, 462, 384], "spans": [{"bbox": [58, 367, 462, 384], "score": 0.998, "content": "平台用户增长放缓，新用户获取成本上升，需要建立系统化的用户增长体系。", "type": "text"}], "index": 14}], "index": 14}, {"type": "title", "bbox": [59, 388, 114, 402], "lines": [{"bbox": [57, 387, 117, 404], "spans": [{"bbox": [57, 387, 117, 404], "score": 0.981, "content": "项目职责：", "type": "text"}], "index": 15}], "index": 15}, {"type": "text", "bbox": [63, 409, 521, 500], "lines": [{"bbox": [63, 411, 520, 425], "spans": [{"bbox": [63, 411, 520, 425], "score": 0.978, "content": "● 主导设计用户裂变增长模型，建立完整的用户增长漏斗，覆盖从获客到留存的全流程", "type": "text"}], "index": 16}, {"bbox": [61, 434, 426, 450], "spans": [{"bbox": [61, 434, 426, 450], "score": 0.966, "content": "• 协调5个部门资源，推动3个核心功能模块开发，确保项目按时交付", "type": "text"}], "index": 17}, {"bbox": [61, 459, 433, 474], "spans": [{"bbox": [61, 459, 433, 474], "score": 0.983, "content": "●建立数据监控体系，定义关键指标，通过A/B测试实时优化增长策略", "type": "text"}], "index": 18}, {"bbox": [62, 484, 328, 499], "spans": [{"bbox": [62, 484, 328, 499], "score": 0.968, "content": "• 设计用户激励体系，提升用户参与度和分享意愿", "type": "text"}], "index": 19}], "index": 17.5}, {"type": "title", "bbox": [59, 508, 114, 523], "lines": [{"bbox": [58, 508, 117, 524], "spans": [{"bbox": [58, 508, 117, 524], "score": 0.972, "content": "项目成果：", "type": "text"}], "index": 20}], "index": 20}, {"type": "text", "bbox": [63, 531, 372, 621], "lines": [{"bbox": [62, 531, 312, 550], "spans": [{"bbox": [62, 531, 177, 550], "score": 0.933, "content": "•6个月内新用户增长", "type": "text"}, {"bbox": [177, 531, 203, 546], "score": 0.86, "content": "4 5 \\%", "type": "inline_equation"}, {"bbox": [203, 531, 285, 550], "score": 0.998, "content": "，获客成本降低", "type": "text"}, {"bbox": [285, 531, 312, 546], "score": 0.82, "content": "2 8 \\%", "type": "inline_equation"}], "index": 21}, {"bbox": [62, 556, 336, 575], "spans": [{"bbox": [62, 556, 158, 575], "score": 0.849, "content": "•用戶留存率提升", "type": "text"}, {"bbox": [159, 556, 184, 570], "score": 0.88, "content": "1 5 \\%", "type": "inline_equation"}, {"bbox": [185, 556, 336, 575], "score": 0.907, "content": "，7日留存达到行业领先水平", "type": "text"}], "index": 22}, {"bbox": [62, 579, 282, 599], "spans": [{"bbox": [62, 579, 147, 599], "score": 0.92, "content": "•年度GMV增长", "type": "text"}, {"bbox": [147, 580, 173, 595], "score": 0.89, "content": "3 0 \\%", "type": "inline_equation"}, {"bbox": [173, 579, 282, 599], "score": 0.999, "content": "，超额完成业务目标", "type": "text"}], "index": 23}, {"bbox": [62, 604, 372, 623], "spans": [{"bbox": [62, 604, 372, 623], "score": 0.971, "content": "• 系统成为公司用户增长标准解决方案，推广至3个业务线", "type": "text"}], "index": 24}], "index": 22.5}], "page_idx": 1, "page_size": [595, 841], "discarded_blocks": [{"type": "discarded", "bbox": [484, 830, 595, 841], "lines": [{"bbox": [484, 830, 596, 842], "spans": [{"bbox": [484, 830, 596, 842], "score": 1.0, "content": "2025/7/23 星期三 16:43", "type": "text"}]}]}, {"type": "discarded", "bbox": [0, 0, 115, 11], "lines": [{"bbox": [0, 0, 115, 10], "spans": [{"bbox": [0, 0, 115, 10], "score": 1.0, "content": "张三 - 高级项目经理简历", "type": "text"}]}]}, {"type": "discarded", "bbox": [304, 0, 593, 11], "lines": [{"bbox": [304, -1, 595, 11], "spans": [{"bbox": [304, -1, 595, 11], "score": 1.0, "content": "file:///C:/Users/<USER>/Desktop/AI%E7%AE%80%E5%8...", "type": "text"}]}]}, {"type": "discarded", "bbox": [0, 829, 55, 841], "lines": [{"bbox": [-1, 830, 56, 843], "spans": [{"bbox": [-1, 830, 56, 843], "score": 1.0, "content": "第2页 共3页", "type": "text"}]}]}], "para_blocks": [{"type": "text", "bbox": [50, 37, 426, 95], "lines": [{"bbox": [50, 38, 426, 52], "spans": [{"bbox": [50, 38, 426, 52], "score": 0.978, "content": "• 负责电商平台用户增长产品线，带领5人产品团队，通过数据驱动优化产品策略", "type": "text"}], "index": 0}, {"bbox": [50, 59, 357, 74], "spans": [{"bbox": [50, 59, 357, 74], "score": 0.974, "content": "• 协调研发、设计、运营等多部门资源，确保项目按时高质量交付", "type": "text"}], "index": 1}, {"bbox": [49, 81, 262, 96], "spans": [{"bbox": [49, 81, 239, 96], "score": 0.946, "content": "•建立了产品标准化流程，提高团队效率", "type": "text"}, {"bbox": [240, 81, 262, 94], "score": 0.82, "content": "2 0 \\%", "type": "inline_equation"}], "index": 2}], "index": 1, "bbox_fs": [49, 38, 426, 96]}, {"type": "title", "bbox": [46, 114, 144, 130], "lines": [{"bbox": [46, 115, 143, 130], "spans": [{"bbox": [46, 115, 143, 130], "score": 0.997, "content": "腾讯科技有限公司", "type": "text"}], "index": 3}], "index": 3}, {"type": "text", "bbox": [451, 115, 550, 130], "lines": [{"bbox": [450, 115, 550, 131], "spans": [{"bbox": [450, 115, 550, 131], "score": 0.999, "content": "2017.07-2019.06", "type": "text"}], "index": 4}], "index": 4, "bbox_fs": [450, 115, 550, 131]}, {"type": "title", "bbox": [46, 138, 96, 153], "lines": [{"bbox": [45, 138, 96, 153], "spans": [{"bbox": [45, 138, 96, 153], "score": 1.0, "content": "项目经理", "type": "text"}], "index": 5}], "index": 5}, {"type": "text", "bbox": [51, 163, 333, 222], "lines": [{"bbox": [50, 164, 333, 180], "spans": [{"bbox": [50, 164, 333, 180], "score": 0.965, "content": "· 负责微信小程序生态产品规划，主导B端工具类小程序开发", "type": "text"}], "index": 6}, {"bbox": [50, 186, 326, 202], "spans": [{"bbox": [50, 186, 326, 202], "score": 0.962, "content": "· 通过用户调研和数据分析，优化产品体验，NPS提升25分", "type": "text"}], "index": 7}, {"bbox": [50, 207, 294, 224], "spans": [{"bbox": [50, 207, 271, 224], "score": 0.973, "content": "·协助制定小程序商业化策略，实现年收入增长", "type": "text"}, {"bbox": [271, 208, 294, 220], "score": 0.82, "content": "5 0 \\%", "type": "inline_equation"}], "index": 8}], "index": 7, "bbox_fs": [50, 164, 333, 224]}, {"type": "title", "bbox": [52, 245, 135, 262], "lines": [{"bbox": [51, 245, 136, 263], "spans": [{"bbox": [51, 245, 136, 263], "score": 1.0, "content": "重点项目经历", "type": "text"}], "index": 9}], "index": 9}, {"type": "title", "bbox": [60, 293, 182, 309], "lines": [{"bbox": [59, 294, 181, 310], "spans": [{"bbox": [59, 294, 181, 310], "score": 0.981, "content": "电商平台用户增长系统", "type": "text"}], "index": 10}], "index": 10}, {"type": "text", "bbox": [451, 293, 539, 308], "lines": [{"bbox": [451, 294, 539, 308], "spans": [{"bbox": [451, 294, 539, 308], "score": 0.999, "content": "2019.10-2020.06", "type": "text"}], "index": 11}], "index": 11, "bbox_fs": [451, 294, 539, 308]}, {"type": "text", "bbox": [60, 320, 185, 334], "lines": [{"bbox": [59, 320, 184, 334], "spans": [{"bbox": [59, 320, 184, 334], "score": 0.994, "content": "项目负责人|阿里巴巴集团", "type": "text"}], "index": 12}], "index": 12, "bbox_fs": [59, 320, 184, 334]}, {"type": "title", "bbox": [59, 345, 114, 360], "lines": [{"bbox": [58, 345, 117, 362], "spans": [{"bbox": [58, 345, 117, 362], "score": 0.944, "content": "项目背景：", "type": "text"}], "index": 13}], "index": 13}, {"type": "text", "bbox": [56, 367, 462, 384], "lines": [{"bbox": [58, 367, 462, 384], "spans": [{"bbox": [58, 367, 462, 384], "score": 0.998, "content": "平台用户增长放缓，新用户获取成本上升，需要建立系统化的用户增长体系。", "type": "text"}], "index": 14}], "index": 14, "bbox_fs": [58, 367, 462, 384]}, {"type": "title", "bbox": [59, 388, 114, 402], "lines": [{"bbox": [57, 387, 117, 404], "spans": [{"bbox": [57, 387, 117, 404], "score": 0.981, "content": "项目职责：", "type": "text"}], "index": 15}], "index": 15}, {"type": "text", "bbox": [63, 409, 521, 500], "lines": [{"bbox": [63, 411, 520, 425], "spans": [{"bbox": [63, 411, 520, 425], "score": 0.978, "content": "● 主导设计用户裂变增长模型，建立完整的用户增长漏斗，覆盖从获客到留存的全流程", "type": "text"}], "index": 16}, {"bbox": [61, 434, 426, 450], "spans": [{"bbox": [61, 434, 426, 450], "score": 0.966, "content": "• 协调5个部门资源，推动3个核心功能模块开发，确保项目按时交付", "type": "text"}], "index": 17}, {"bbox": [61, 459, 433, 474], "spans": [{"bbox": [61, 459, 433, 474], "score": 0.983, "content": "●建立数据监控体系，定义关键指标，通过A/B测试实时优化增长策略", "type": "text"}], "index": 18}, {"bbox": [62, 484, 328, 499], "spans": [{"bbox": [62, 484, 328, 499], "score": 0.968, "content": "• 设计用户激励体系，提升用户参与度和分享意愿", "type": "text"}], "index": 19}], "index": 17.5, "bbox_fs": [61, 411, 520, 499]}, {"type": "title", "bbox": [59, 508, 114, 523], "lines": [{"bbox": [58, 508, 117, 524], "spans": [{"bbox": [58, 508, 117, 524], "score": 0.972, "content": "项目成果：", "type": "text"}], "index": 20}], "index": 20}, {"type": "text", "bbox": [63, 531, 372, 621], "lines": [{"bbox": [62, 531, 312, 550], "spans": [{"bbox": [62, 531, 177, 550], "score": 0.933, "content": "•6个月内新用户增长", "type": "text"}, {"bbox": [177, 531, 203, 546], "score": 0.86, "content": "4 5 \\%", "type": "inline_equation"}, {"bbox": [203, 531, 285, 550], "score": 0.998, "content": "，获客成本降低", "type": "text"}, {"bbox": [285, 531, 312, 546], "score": 0.82, "content": "2 8 \\%", "type": "inline_equation"}], "index": 21}, {"bbox": [62, 556, 336, 575], "spans": [{"bbox": [62, 556, 158, 575], "score": 0.849, "content": "•用戶留存率提升", "type": "text"}, {"bbox": [159, 556, 184, 570], "score": 0.88, "content": "1 5 \\%", "type": "inline_equation"}, {"bbox": [185, 556, 336, 575], "score": 0.907, "content": "，7日留存达到行业领先水平", "type": "text"}], "index": 22}, {"bbox": [62, 579, 282, 599], "spans": [{"bbox": [62, 579, 147, 599], "score": 0.92, "content": "•年度GMV增长", "type": "text"}, {"bbox": [147, 580, 173, 595], "score": 0.89, "content": "3 0 \\%", "type": "inline_equation"}, {"bbox": [173, 579, 282, 599], "score": 0.999, "content": "，超额完成业务目标", "type": "text"}], "index": 23}, {"bbox": [62, 604, 372, 623], "spans": [{"bbox": [62, 604, 372, 623], "score": 0.971, "content": "• 系统成为公司用户增长标准解决方案，推广至3个业务线", "type": "text"}], "index": 24}], "index": 22.5, "bbox_fs": [62, 531, 372, 623]}]}, {"preproc_blocks": [{"type": "title", "bbox": [59, 49, 169, 65], "lines": [{"bbox": [59, 49, 169, 65], "spans": [{"bbox": [59, 49, 169, 65], "score": 1.0, "content": "企业服务小程序平台", "type": "text"}], "index": 0}], "index": 0}, {"type": "text", "bbox": [59, 75, 174, 90], "lines": [{"bbox": [59, 75, 174, 89], "spans": [{"bbox": [59, 75, 174, 89], "score": 0.997, "content": "核心项目经理|腾讯科技", "type": "text"}], "index": 1}], "index": 1}, {"type": "title", "bbox": [59, 100, 114, 115], "lines": [{"bbox": [58, 100, 116, 117], "spans": [{"bbox": [58, 100, 116, 117], "score": 0.95, "content": "项目背景:", "type": "text"}], "index": 2}], "index": 2}, {"type": "text", "bbox": [57, 123, 438, 139], "lines": [{"bbox": [58, 122, 439, 140], "spans": [{"bbox": [58, 122, 439, 140], "score": 0.999, "content": "企业用户对小程序工具需求旺盛，但缺乏标准化解决方案，开发成本高。", "type": "text"}], "index": 3}], "index": 3}, {"type": "title", "bbox": [59, 142, 114, 158], "lines": [{"bbox": [57, 142, 118, 159], "spans": [{"bbox": [57, 142, 118, 159], "score": 0.99, "content": "项目职责：", "type": "text"}], "index": 4}], "index": 4}, {"type": "text", "bbox": [63, 164, 397, 255], "lines": [{"bbox": [62, 166, 384, 182], "spans": [{"bbox": [62, 166, 384, 182], "score": 0.976, "content": "● 设计B端小程序开放平台架构和核心功能，定义产品路线图", "type": "text"}], "index": 5}, {"bbox": [63, 191, 365, 207], "spans": [{"bbox": [63, 191, 365, 207], "score": 0.981, "content": "• 主导零售和餐饮行业解决方案开发，建立标准化组件库", "type": "text"}], "index": 6}, {"bbox": [61, 214, 397, 232], "spans": [{"bbox": [61, 214, 98, 232], "score": 0.867, "content": "·通过", "type": "text"}, {"bbox": [99, 215, 119, 229], "score": 0.86, "content": "{ 2 0 + }", "type": "inline_equation"}, {"bbox": [120, 214, 397, 232], "score": 1.0, "content": "企业深度调研，提炼共性需求，设计可配置功能模块", "type": "text"}], "index": 7}, {"bbox": [61, 238, 323, 257], "spans": [{"bbox": [61, 238, 323, 257], "score": 0.956, "content": "● 制定项目管理流程，确保3个并行项目按时交付", "type": "text"}], "index": 8}], "index": 6.5}, {"type": "title", "bbox": [59, 263, 114, 278], "lines": [{"bbox": [58, 263, 117, 279], "spans": [{"bbox": [58, 263, 117, 279], "score": 0.971, "content": "项目成果：", "type": "text"}], "index": 9}], "index": 9}, {"type": "text", "bbox": [63, 285, 391, 376], "lines": [{"bbox": [62, 287, 391, 303], "spans": [{"bbox": [62, 287, 195, 303], "score": 0.962, "content": "•累计服务企业用户10万", "type": "text"}, {"bbox": [196, 288, 204, 299], "score": 0.59, "content": "+", "type": "inline_equation"}, {"bbox": [204, 287, 391, 303], "score": 0.998, "content": "，覆盖零售、餐饮、教育等6个行业", "type": "text"}], "index": 10}, {"bbox": [61, 309, 281, 330], "spans": [{"bbox": [61, 309, 134, 330], "score": 0.917, "content": "·年收入增长", "type": "text"}, {"bbox": [135, 311, 160, 325], "score": 0.88, "content": "5 0 \\%", "type": "inline_equation"}, {"bbox": [160, 309, 254, 330], "score": 0.998, "content": "，付费转化率提升", "type": "text"}, {"bbox": [255, 311, 281, 325], "score": 0.79, "content": "3 5 \\%", "type": "inline_equation"}], "index": 11}, {"bbox": [62, 335, 257, 353], "spans": [{"bbox": [62, 335, 257, 353], "score": 0.947, "content": "• NPS提升25分，达到行业领先水平", "type": "text"}], "index": 12}, {"bbox": [62, 359, 379, 378], "spans": [{"bbox": [62, 359, 146, 378], "score": 0.874, "content": "• 开发效率提升", "type": "text"}, {"bbox": [146, 360, 172, 374], "score": 0.88, "content": "6 0 \\%", "type": "inline_equation"}, {"bbox": [172, 359, 379, 378], "score": 0.986, "content": "，企业小程序上线周期从2周缩短至3天", "type": "text"}], "index": 13}], "index": 11.5}, {"type": "title", "bbox": [52, 422, 108, 438], "lines": [{"bbox": [51, 421, 109, 440], "spans": [{"bbox": [51, 421, 109, 440], "score": 1.0, "content": "专业技能", "type": "text"}], "index": 14}], "index": 14}, {"type": "table", "bbox": [52, 457, 527, 509], "blocks": [{"type": "table_body", "bbox": [52, 457, 527, 509], "group_id": 0, "lines": [{"bbox": [52, 457, 527, 509], "spans": [{"bbox": [52, 457, 527, 509], "score": 0.797, "html": "<html><body><table><tr><td>项目管理</td><td>团队领导</td><td>风险管理</td><td>敏捷开发</td><td>Scrum</td><td>JIRA</td><td>项目规划</td><td>资源协调</td></tr><tr><td>跨部门协作</td><td>数据分析</td><td></td><td></td><td></td><td></td><td></td><td></td></tr></table></body></html>", "type": "table", "image_path": "4e575132c5bf09adcefb013b5d54e8cc09d330bc3b97fa59cdfae116821d361e.jpg"}]}], "index": 16, "virtual_lines": [{"bbox": [52, 457, 527, 474.3333333333333], "spans": [], "index": 15}, {"bbox": [52, 474.3333333333333, 527, 491.66666666666663], "spans": [], "index": 16}, {"bbox": [52, 491.66666666666663, 527, 508.99999999999994], "spans": [], "index": 17}]}], "index": 16}, {"type": "title", "bbox": [52, 532, 108, 549], "lines": [{"bbox": [51, 532, 109, 551], "spans": [{"bbox": [51, 532, 109, 551], "score": 1.0, "content": "自我评价", "type": "text"}], "index": 18}], "index": 18}, {"type": "text", "bbox": [45, 569, 549, 644], "lines": [{"bbox": [47, 570, 547, 584], "spans": [{"bbox": [47, 570, 547, 584], "score": 0.995, "content": "7年互联网项目管理经验，PMP认证项目经理。擅长从0到1的项目规划与执行，具备出色的团队", "type": "text"}], "index": 19}, {"bbox": [47, 590, 539, 603], "spans": [{"bbox": [47, 590, 539, 603], "score": 1.0, "content": "领导和跨部门协调能力。熟悉敏捷开发流程，能够有效管理项目风险并确保按时交付。通过数", "type": "text"}], "index": 20}, {"bbox": [46, 609, 540, 623], "spans": [{"bbox": [46, 609, 540, 623], "score": 1.0, "content": "据驱动决策，持续优化项目管理流程，提高团队效率。拥有丰富的电商和企业服务项目管理经", "type": "text"}], "index": 21}, {"bbox": [47, 629, 233, 642], "spans": [{"bbox": [47, 629, 233, 642], "score": 1.0, "content": "验，成功领导多个百万级用户项目。", "type": "text"}], "index": 22}], "index": 20.5}], "page_idx": 2, "page_size": [595, 841], "discarded_blocks": [{"type": "discarded", "bbox": [0, 0, 115, 11], "lines": [{"bbox": [0, 0, 115, 10], "spans": [{"bbox": [0, 0, 115, 10], "score": 1.0, "content": "张三 - 高级项目经理简历", "type": "text"}]}]}, {"type": "discarded", "bbox": [484, 830, 595, 841], "lines": [{"bbox": [484, 830, 596, 842], "spans": [{"bbox": [484, 830, 596, 842], "score": 1.0, "content": "2025/7/23 星期三 16:43", "type": "text"}]}]}, {"type": "discarded", "bbox": [304, 0, 593, 11], "lines": [{"bbox": [304, -1, 595, 11], "spans": [{"bbox": [304, -1, 595, 11], "score": 1.0, "content": "file:///C:/Users/<USER>/Desktop/AI%E7%AE%80%E5%8...", "type": "text"}]}]}, {"type": "discarded", "bbox": [452, 49, 538, 62], "lines": [{"bbox": [452, 50, 538, 62], "spans": [{"bbox": [452, 50, 538, 62], "score": 0.999, "content": "2018.03-2019.05", "type": "text"}]}]}, {"type": "discarded", "bbox": [0, 829, 55, 841], "lines": [{"bbox": [-1, 830, 56, 843], "spans": [{"bbox": [-1, 830, 56, 843], "score": 1.0, "content": "第3页 共3页", "type": "text"}]}]}], "para_blocks": [{"type": "title", "bbox": [59, 49, 169, 65], "lines": [{"bbox": [59, 49, 169, 65], "spans": [{"bbox": [59, 49, 169, 65], "score": 1.0, "content": "企业服务小程序平台", "type": "text"}], "index": 0}], "index": 0}, {"type": "text", "bbox": [59, 75, 174, 90], "lines": [{"bbox": [59, 75, 174, 89], "spans": [{"bbox": [59, 75, 174, 89], "score": 0.997, "content": "核心项目经理|腾讯科技", "type": "text"}], "index": 1}], "index": 1, "bbox_fs": [59, 75, 174, 89]}, {"type": "title", "bbox": [59, 100, 114, 115], "lines": [{"bbox": [58, 100, 116, 117], "spans": [{"bbox": [58, 100, 116, 117], "score": 0.95, "content": "项目背景:", "type": "text"}], "index": 2}], "index": 2}, {"type": "text", "bbox": [57, 123, 438, 139], "lines": [{"bbox": [58, 122, 439, 140], "spans": [{"bbox": [58, 122, 439, 140], "score": 0.999, "content": "企业用户对小程序工具需求旺盛，但缺乏标准化解决方案，开发成本高。", "type": "text"}], "index": 3}], "index": 3, "bbox_fs": [58, 122, 439, 140]}, {"type": "title", "bbox": [59, 142, 114, 158], "lines": [{"bbox": [57, 142, 118, 159], "spans": [{"bbox": [57, 142, 118, 159], "score": 0.99, "content": "项目职责：", "type": "text"}], "index": 4}], "index": 4}, {"type": "text", "bbox": [63, 164, 397, 255], "lines": [{"bbox": [62, 166, 384, 182], "spans": [{"bbox": [62, 166, 384, 182], "score": 0.976, "content": "● 设计B端小程序开放平台架构和核心功能，定义产品路线图", "type": "text"}], "index": 5}, {"bbox": [63, 191, 365, 207], "spans": [{"bbox": [63, 191, 365, 207], "score": 0.981, "content": "• 主导零售和餐饮行业解决方案开发，建立标准化组件库", "type": "text"}], "index": 6}, {"bbox": [61, 214, 397, 232], "spans": [{"bbox": [61, 214, 98, 232], "score": 0.867, "content": "·通过", "type": "text"}, {"bbox": [99, 215, 119, 229], "score": 0.86, "content": "{ 2 0 + }", "type": "inline_equation"}, {"bbox": [120, 214, 397, 232], "score": 1.0, "content": "企业深度调研，提炼共性需求，设计可配置功能模块", "type": "text"}], "index": 7}, {"bbox": [61, 238, 323, 257], "spans": [{"bbox": [61, 238, 323, 257], "score": 0.956, "content": "● 制定项目管理流程，确保3个并行项目按时交付", "type": "text"}], "index": 8}], "index": 6.5, "bbox_fs": [61, 166, 397, 257]}, {"type": "title", "bbox": [59, 263, 114, 278], "lines": [{"bbox": [58, 263, 117, 279], "spans": [{"bbox": [58, 263, 117, 279], "score": 0.971, "content": "项目成果：", "type": "text"}], "index": 9}], "index": 9}, {"type": "list", "bbox": [63, 285, 391, 376], "lines": [{"bbox": [62, 287, 391, 303], "spans": [{"bbox": [62, 287, 195, 303], "score": 0.962, "content": "•累计服务企业用户10万", "type": "text"}, {"bbox": [196, 288, 204, 299], "score": 0.59, "content": "+", "type": "inline_equation"}, {"bbox": [204, 287, 391, 303], "score": 0.998, "content": "，覆盖零售、餐饮、教育等6个行业", "type": "text"}], "index": 10}, {"bbox": [61, 309, 281, 330], "spans": [{"bbox": [61, 309, 134, 330], "score": 0.917, "content": "·年收入增长", "type": "text"}, {"bbox": [135, 311, 160, 325], "score": 0.88, "content": "5 0 \\%", "type": "inline_equation"}, {"bbox": [160, 309, 254, 330], "score": 0.998, "content": "，付费转化率提升", "type": "text"}, {"bbox": [255, 311, 281, 325], "score": 0.79, "content": "3 5 \\%", "type": "inline_equation"}], "index": 11, "is_list_end_line": true}, {"bbox": [62, 335, 257, 353], "spans": [{"bbox": [62, 335, 257, 353], "score": 0.947, "content": "• NPS提升25分，达到行业领先水平", "type": "text"}], "index": 12, "is_list_start_line": true, "is_list_end_line": true}, {"bbox": [62, 359, 379, 378], "spans": [{"bbox": [62, 359, 146, 378], "score": 0.874, "content": "• 开发效率提升", "type": "text"}, {"bbox": [146, 360, 172, 374], "score": 0.88, "content": "6 0 \\%", "type": "inline_equation"}, {"bbox": [172, 359, 379, 378], "score": 0.986, "content": "，企业小程序上线周期从2周缩短至3天", "type": "text"}], "index": 13, "is_list_start_line": true}], "index": 11.5, "bbox_fs": [61, 287, 391, 378]}, {"type": "title", "bbox": [52, 422, 108, 438], "lines": [{"bbox": [51, 421, 109, 440], "spans": [{"bbox": [51, 421, 109, 440], "score": 1.0, "content": "专业技能", "type": "text"}], "index": 14}], "index": 14}, {"type": "table", "bbox": [52, 457, 527, 509], "blocks": [{"type": "table_body", "bbox": [52, 457, 527, 509], "group_id": 0, "lines": [{"bbox": [52, 457, 527, 509], "spans": [{"bbox": [52, 457, 527, 509], "score": 0.797, "html": "<html><body><table><tr><td>项目管理</td><td>团队领导</td><td>风险管理</td><td>敏捷开发</td><td>Scrum</td><td>JIRA</td><td>项目规划</td><td>资源协调</td></tr><tr><td>跨部门协作</td><td>数据分析</td><td></td><td></td><td></td><td></td><td></td><td></td></tr></table></body></html>", "type": "table", "image_path": "4e575132c5bf09adcefb013b5d54e8cc09d330bc3b97fa59cdfae116821d361e.jpg"}]}], "index": 16, "virtual_lines": [{"bbox": [52, 457, 527, 474.3333333333333], "spans": [], "index": 15}, {"bbox": [52, 474.3333333333333, 527, 491.66666666666663], "spans": [], "index": 16}, {"bbox": [52, 491.66666666666663, 527, 508.99999999999994], "spans": [], "index": 17}]}], "index": 16}, {"type": "title", "bbox": [52, 532, 108, 549], "lines": [{"bbox": [51, 532, 109, 551], "spans": [{"bbox": [51, 532, 109, 551], "score": 1.0, "content": "自我评价", "type": "text"}], "index": 18}], "index": 18}, {"type": "text", "bbox": [45, 569, 549, 644], "lines": [{"bbox": [47, 570, 547, 584], "spans": [{"bbox": [47, 570, 547, 584], "score": 0.995, "content": "7年互联网项目管理经验，PMP认证项目经理。擅长从0到1的项目规划与执行，具备出色的团队", "type": "text"}], "index": 19}, {"bbox": [47, 590, 539, 603], "spans": [{"bbox": [47, 590, 539, 603], "score": 1.0, "content": "领导和跨部门协调能力。熟悉敏捷开发流程，能够有效管理项目风险并确保按时交付。通过数", "type": "text"}], "index": 20}, {"bbox": [46, 609, 540, 623], "spans": [{"bbox": [46, 609, 540, 623], "score": 1.0, "content": "据驱动决策，持续优化项目管理流程，提高团队效率。拥有丰富的电商和企业服务项目管理经", "type": "text"}], "index": 21}, {"bbox": [47, 629, 233, 642], "spans": [{"bbox": [47, 629, 233, 642], "score": 1.0, "content": "验，成功领导多个百万级用户项目。", "type": "text"}], "index": 22}], "index": 20.5, "bbox_fs": [46, 570, 547, 642]}]}], "_backend": "pipeline", "_version_name": "2.1.4"}