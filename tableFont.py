#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Word 文档表格字体设置工具

文件描述：
    这是一个专门用于批量设置 Word 文档中所有表格字体样式的工具。
    可以统一设置表格中所有文本的字体名称和字号大小。

主要功能：
    1. 批量字体设置：一次性设置文档中所有表格的字体
    2. 字体名称设置：支持设置指定的字体名称（如宋体、微软雅黑等）
    3. 字号大小设置：支持设置字体大小（以磅为单位）
    4. 保持文档结构：只修改字体样式，不影响文档的其他格式
    5. 批量处理：适合处理包含大量表格的文档

技术特点：
    - 使用 python-docx 库操作 Word 文档
    - 遍历所有表格的每个单元格和段落
    - 精确控制字体属性设置
    - 支持中文字体设置

处理流程：
    1. 加载指定的 Word 文档
    2. 遍历文档中的所有表格
    3. 对每个表格的每行每列进行处理
    4. 设置每个单元格中所有文本的字体属性
    5. 保存修改后的文档

配置参数：
    - 输入文件：软件测试计划z.docx
    - 输出文件：output.docx
    - 字体名称：宋体
    - 字体大小：12磅（小四号）

使用场景：
    - 文档格式标准化
    - 批量文档样式统一
    - 测试文档格式规范化
    - 企业文档模板制作

注意事项：
    - 会覆盖表格中现有的字体设置
    - 建议在处理前备份原始文档
    - 仅处理表格内容，不影响正文

作者：文档格式化团队
创建时间：2025年
版本：1.0
"""

from docx import Document
from docx.shared import Pt

def set_table_font_size(doc_path, output_path, font_name=None, font_size=12):
    """
    将 Word 文档中所有表格的字体设置为指定大小
    :param doc_path: 输入文档路径（.docx）
    :param output_path: 输出文档路径
    :param font_name: 字体名称（如 "宋体"，可选）
    :param font_size: 字号（小四=12磅）
    """
    doc = Document(doc_path)
    
    for table in doc.tables:
        for row in table.rows:
            for cell in row.cells:
                for paragraph in cell.paragraphs:
                    for run in paragraph.runs:
                        if font_name:
                            run.font.name = font_name
                        run.font.size = Pt(font_size)
    
    doc.save(output_path)
    print(f"处理完成！结果已保存至: {output_path}")

# 使用示例（替换以下路径）
input_file = "软件测试计划z.docx"    # 输入文件路径
output_file = "output.docx"  # 输出文件路径

set_table_font_size(input_file, output_file, font_name="宋体", font_size=12)