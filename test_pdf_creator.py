#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MinerU 功能测试 PDF 文档生成器

文件描述：
    这是一个用于创建测试 PDF 文档的工具，专门用于验证 MinerU 的解析功能。
    生成包含多种内容类型的 PDF 文档，用于测试 MinerU 的各项解析能力。

主要功能：
    1. 多内容类型测试：创建包含文本、表格、公式等多种内容的 PDF
    2. 中英文混合：测试多语言内容的解析能力
    3. 数学公式：包含常见的数学公式用于公式识别测试
    4. 表格结构：创建简单的表格结构用于表格解析测试
    5. 格式化文本：包含不同字体大小和样式的文本

技术特点：
    - 使用 ReportLab 库生成 PDF 文档
    - 支持多种字体和字号设置
    - 自动分页处理，避免内容溢出
    - 生成标准化的测试内容

测试内容包括：
    - 标题和段落文本
    - 数学公式（E=mc²、积分公式等）
    - 简单表格（姓名、年龄、城市）
    - 列表和结构化内容
    - 混合语言文本

生成的 PDF 特点：
    - 标准 Letter 页面大小
    - 使用 Helvetica 字体
    - 包含多种测试场景
    - 适合 MinerU 解析测试

输出文件：test_document.pdf

使用场景：
    - MinerU 功能验证
    - PDF 解析能力测试
    - 开发环境搭建验证
    - 解析质量基准测试

依赖要求：
    - reportlab 库：pip install reportlab

作者：测试工具团队
创建时间：2025年
版本：1.0
"""

from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import os

def create_test_pdf():
    """创建一个包含中英文文本、表格和公式的测试 PDF"""
    
    # 创建 PDF 文件
    filename = "test_document.pdf"
    c = canvas.Canvas(filename, pagesize=letter)
    width, height = letter
    
    # 设置字体（使用系统默认字体）
    c.setFont("Helvetica", 16)
    
    # 添加标题
    c.drawString(100, height - 100, "MinerU Test Document")
    
    # 添加英文段落
    c.setFont("Helvetica", 12)
    y_position = height - 150
    
    text_lines = [
        "This is a test document for MinerU PDF parsing capabilities.",
        "It contains various types of content including:",
        "- Regular text paragraphs",
        "- Mathematical formulas",
        "- Tables and structured data",
        "- Mixed language content",
        "",
        "Mathematical Formula Example:",
        "E = mc²",
        "∫ f(x)dx = F(x) + C",
        "",
        "Table Example:",
        "Name        Age    City",
        "Alice       25     Beijing",
        "Bob         30     Shanghai", 
        "Charlie     28     Guangzhou",
        "",
        "This document tests the extraction capabilities of MinerU",
        "for converting PDF content to Markdown format.",
    ]
    
    for line in text_lines:
        c.drawString(100, y_position, line)
        y_position -= 20
        
        # 如果页面空间不够，创建新页面
        if y_position < 100:
            c.showPage()
            c.setFont("Helvetica", 12)
            y_position = height - 100
    
    # 保存 PDF
    c.save()
    print(f"测试 PDF 文件已创建: {filename}")
    return filename

if __name__ == "__main__":
    try:
        pdf_file = create_test_pdf()
        print(f"成功创建测试文件: {pdf_file}")
    except ImportError:
        print("需要安装 reportlab 库: pip install reportlab")
    except Exception as e:
        print(f"创建 PDF 时出错: {e}")
