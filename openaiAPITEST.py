
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OpenAI API 连接测试工具

文件描述：
    这是一个用于测试 OpenAI API 连接和功能的脚本。
    提供完整的 API 密钥验证、连接测试和错误处理功能。

主要功能：
    1. 从环境变量或用户输入获取 API 密钥
    2. 验证 API 密钥格式的正确性
    3. 测试与 OpenAI API 的连接
    4. 发送测试请求并获取响应
    5. 提供详细的错误诊断和解决方案

技术特点：
    - 支持多种 API 密钥获取方式
    - 完善的错误处理和用户友好的提示
    - 使用 gpt-4o-mini 模型进行测试
    - 提供详细的故障排除指南

安全特性：
    - API 密钥格式验证
    - 不在代码中硬编码敏感信息
    - 支持环境变量方式管理密钥

错误处理：
    - 网络连接问题诊断
    - API 密钥有效性检查
    - 账户余额不足提醒
    - 代理设置建议

使用场景：
    - OpenAI API 集成前的连接测试
    - API 密钥有效性验证
    - 网络环境和配置检查
    - AI 应用开发环境搭建

配置要求：
    - 有效的 OpenAI API 密钥
    - 稳定的网络连接
    - 足够的账户余额

作者：AI 开发团队
创建时间：2025年
版本：1.0
"""

from openai import OpenAI
import os

# 设置 OpenAI API 密钥
# 方法1：从环境变量读取（推荐）
api_key = os.getenv('sk-HIIBrUSWckvVQWuiwuLy7lvlaPtlJkID4ft9aE3O55Be6mvU')

# 方法2：如果环境变量不存在，提示用户输入
if not api_key:
    print("未找到 OPENAI_API_KEY 环境变量")
    print("请输入您的 OpenAI API 密钥：")
    api_key = input().strip()

# 检查 API 密钥格式
if not api_key or not api_key.startswith('sk-'):
    print("错误：API 密钥格式不正确或为空")
    print("正确的 API 密钥应该以 'sk-' 开头")
    print("您可以在 https://platform.openai.com/account/api-keys 获取有效的 API 密钥")
    exit(1)

try:
    client = OpenAI(api_key=api_key)

    print("正在连接 OpenAI API...")

    # 调用 OpenAI API 生成文本
    response = client.chat.completions.create(
        model="gpt-4o-mini",  # 使用的模型
        messages=[
            {"role": "user", "content": "你好，AI！能否帮我写一段简单的代码来测试 OpenAI API？"}
        ],
        max_tokens=100  # 设置生成的最大 token 数量
    )

    # 输出生成的文本
    print("API 调用成功！")
    print("生成的回复：")
    print(response.choices[0].message.content.strip())

except Exception as e:
    print(f"API 调用失败：{type(e).__name__}: {e}")
    print("\n可能的解决方案：")
    print("1. 检查网络连接")
    print("2. 检查 API 密钥是否有效")
    print("3. 检查是否需要设置代理")
    print("4. 确认账户余额是否充足")
