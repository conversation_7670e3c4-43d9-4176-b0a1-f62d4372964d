#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ollama 本地大模型测试工具

文件描述：
    这是一个用于测试本地部署的 Ollama 大语言模型的脚本。
    通过 OpenAI 兼容的 API 接口与本地运行的 Ollama 服务进行交互。

主要功能：
    1. 连接本地 Ollama 服务（端口 11434）
    2. 使用 OpenAI 兼容的 API 格式发送请求
    3. 测试 deepseek-r1:7b 模型的对话能力
    4. 验证本地大模型部署是否正常工作

技术特点：
    - 使用 OpenAI Python 客户端库
    - 兼容 OpenAI API 格式，便于代码迁移
    - 支持自定义温度和最大 token 数参数
    - 本地部署，无需网络连接和 API 费用

配置参数：
    - 服务地址：http://localhost:11434/v1
    - 模型：deepseek-r1:7b
    - 温度：0.7（控制生成多样性）
    - 最大 tokens：512

使用场景：
    - 本地大模型功能验证
    - AI 应用开发测试
    - 离线环境下的智能对话
    - 模型性能基准测试

前置条件：
    - 已安装并启动 Ollama 服务
    - 已下载 deepseek-r1:7b 模型
    - 安装 openai Python 包

作者：AI 开发团队
创建时间：2025年
版本：1.0
"""

from openai import OpenAI

# 初始化客户端，指向 Ollama 的本地服务
client = OpenAI(
    base_url="http://localhost:11434/v1",  # Ollama API 地址
    api_key="ollama"  # Ollama 默认无需真实 API Key，填任意值即可
)

# 发送请求
response = client.chat.completions.create(
    model="deepseek-r1:7b",  # 指定模型
    messages=[
        {"role": "system", "content": "你是一个有帮助的助手。"},
        {"role": "user", "content": "你好，什么是大模型？"}
    ],
    temperature=0.7,  # 控制生成多样性
    max_tokens=512    # 最大生成 token 数
)

# 打印结果
print(response.choices[0].message.content)