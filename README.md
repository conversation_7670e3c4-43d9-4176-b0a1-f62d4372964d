# MinerU 本地部署项目

## 项目简介

MinerU 是一个高质量的 PDF 转 Markdown 和 JSON 的工具，支持多种文档解析功能：

- **PDF 解析**：将 PDF 文档转换为 Markdown 格式
- **多语言支持**：支持中文、英文、日文、韩文等多种语言
- **智能识别**：自动识别文本、表格、公式、图片等内容
- **多种输出格式**：支持 Markdown、JSON 等格式输出
- **Web 界面**：提供友好的 Gradio Web 界面

## 部署环境

- **操作系统**：Windows 11
- **Python 版本**：3.11.2
- **虚拟环境**：已激活 (.venv)
- **包管理器**：pip + uv

## 安装步骤

### 1. 环境准备

确保系统已安装：
- Python 3.10-3.13
- Git
- pip

### 2. 安装 MinerU

```bash
# 升级 pip
pip install --upgrade pip

# 安装 uv（更快的包管理器）
pip install uv

# 安装 MinerU 核心包
uv pip install -U "mineru[core]"
```

### 3. 下载模型

```bash
# 下载 pipeline 模型（推荐）
python -m mineru.cli.models_download -s huggingface -m pipeline

# 或下载所有模型
python -m mineru.cli.models_download -s huggingface -m all
```

### 4. 验证安装

```bash
# 检查安装
python -c "import mineru; print('MinerU 安装成功！')"

# 查看帮助
python -m mineru.cli.client --help
```

## 使用方法

### 命令行使用

```bash
# 基本用法
python -m mineru.cli.client -p input.pdf -o output_dir

# 指定语言
python -m mineru.cli.client -p input.pdf -o output_dir -l ch

# 指定页面范围
python -m mineru.cli.client -p input.pdf -o output_dir -s 0 -e 5

# 启用/禁用功能
python -m mineru.cli.client -p input.pdf -o output_dir -f true -t true
```

### Web 界面使用

```bash
# 启动 Gradio Web 界面
mineru-gradio
```

然后在浏览器中访问：http://127.0.0.1:7860

### API 服务

```bash
# 启动 API 服务
mineru-api
```

## 配置文件

配置文件位置：`C:\Users\<USER>\mineru.json`

主要配置项：
- `models-dir`：模型文件路径
- `latex-delimiter-config`：LaTeX 公式分隔符配置
- `llm-aided-config`：LLM 辅助配置

## 输出格式

MinerU 会在输出目录生成以下文件：

- `*.md`：Markdown 格式的文档内容
- `*_content_list.json`：结构化的内容列表
- `*_layout.pdf`：布局分析结果
- `*_model.json`：模型识别结果
- `images/`：提取的图片文件

## 测试示例

项目包含测试文件：
- `test_pdf_creator.py`：创建测试 PDF 的脚本
- `test_document.pdf`：生成的测试 PDF 文件
- `output/`：解析结果输出目录

## 常见问题

### 1. 模型下载慢
- 使用国内镜像：`-s modelscope`
- 或使用代理

### 2. 内存不足
- 使用 `--vram` 参数限制显存使用
- 或使用 CPU 模式：`-d cpu`

### 3. OCR 识别不准确
- 指定正确的语言：`-l ch` (中文) 或 `-l en` (英文)
- 确保 PDF 质量良好

## 性能优化

- **GPU 加速**：支持 CUDA、NPU、MPS
- **批处理**：支持批量处理多个文件
- **内存管理**：可配置显存使用上限

## 项目结构

```
d:\code\demo\
├── README.md                 # 项目说明文档
├── test_pdf_creator.py      # 测试 PDF 创建脚本
├── test_document.pdf        # 测试 PDF 文件
├── output/                  # 输出目录
│   └── test_document/
│       └── auto/
│           ├── test_document.md
│           ├── test_document_content_list.json
│           └── images/
└── .venv/                   # 虚拟环境
```

## 相关链接

- **GitHub 仓库**：https://github.com/opendatalab/MinerU
- **官方文档**：https://github.com/opendatalab/MinerU/blob/main/README.md
- **在线演示**：https://huggingface.co/spaces/opendatalab/PDF-Extract-Kit

## 更新日志

- **2025-07-24**：完成本地部署，测试基本功能正常
- 安装版本：mineru==2.1.4
- 模型版本：PDF-Extract-Kit-1.0

---

**部署完成！** MinerU 已成功部署并可正常使用。
