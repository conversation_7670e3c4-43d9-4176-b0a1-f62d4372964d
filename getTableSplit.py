#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Word 文档表格拆分工具

文件描述：
    这是一个专门用于处理 Word 文档中大型表格的拆分工具。
    主要功能是将超过9行的表格拆分成多个较小的表格，每个表格保留前9行和一行数据行。

主要功能：
    1. 自动检测文档中超过9行的表格
    2. 为每个超出的数据行创建独立的表格副本
    3. 保持原始表格的格式和样式
    4. 复制单元格内容和段落格式
    5. 生成新的文档文件

处理逻辑：
    - 扫描文档中的所有表格
    - 对于行数 > 9 的表格：
      * 计算需要拆分的次数（总行数 - 9）
      * 为每个额外行创建完整的表格副本
      * 保留前9行（通常是表头和基础结构）
      * 保留一行对应的数据行
      * 删除其他多余的数据行

技术特点：
    - 使用 python-docx 库操作 Word 文档
    - 完整保留表格格式和样式
    - 支持复杂表格结构的处理
    - 自动处理表格行的添加和删除

使用场景：
    - 软件测试文档的表格标准化处理
    - 大型表格的分页显示优化
    - 文档格式规范化处理
    - 批量文档处理自动化

输入文件：任务信息处理仿真系统软件测试大纲.docx
输出文件：modified_document.docx

注意事项：
    - 会修改原始表格结构
    - 建议在处理前备份原始文档
    - 适用于特定格式的测试文档

作者：文档处理团队
创建时间：2025年
版本：1.0
"""

from docx import Document
from copy import deepcopy

# 加载 Word 文档
doc = Document('任务信息处理仿真系统软件测试大纲.docx')

# 遍历文档中的所有表格
for table in doc.tables:
    # 获取表格的总行数
    total_rows = len(table.rows)
    
    # 如果表格的行数超过9行
    if total_rows > 9:
        # 计算需要复制的次数（第9行之后的行数）
        copy_times = total_rows - 9
        
        # 复制表格多次
        for i in range(copy_times):
            # 复制整个表格
            new_table = doc.add_table(rows=total_rows, cols=len(table.columns))
            
            # 复制表格格式
            new_table.style = table.style
            
            # 复制单元格内容和格式
            for row_idx, row in enumerate(table.rows):
                for col_idx, cell in enumerate(row.cells):
                    # 复制单元格文本和格式
                    new_cell = new_table.rows[row_idx].cells[col_idx]
                    new_cell.text = cell.text
                    # 复制段落格式
                    if cell.paragraphs:
                        new_cell.paragraphs[0].style = cell.paragraphs[0].style
            
            # 保留前9行和第(9+i+1)行，删除其他行
            current_keep_row = 9 + i  # 要保留的行索引
            rows_to_delete = []
            
            # 从后往前记录要删除的行索引
            for row_idx in range(len(new_table.rows) - 1, -1, -1):
                if row_idx > 9 and row_idx != current_keep_row:
                    rows_to_delete.append(row_idx)
            
            # 删除不需要的行
            for row_idx in rows_to_delete:
                tr = new_table._tbl.tr_lst[row_idx]
                new_table._tbl.tr_lst.remove(tr)

# 保存修改后的文档
doc.save('modified_document.docx')