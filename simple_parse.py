#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MinerU PDF 简历解析工具 - 简洁版（推荐使用）

文件描述：
    这是一个简洁易用的 PDF 简历解析工具，专为快速解析和查看结果而设计。
    使用 MinerU 框架通过命令行方式解析 PDF 文档，并直接显示解析结果。

主要功能：
    1. 快速 PDF 解析：使用 MinerU 命令行工具进行高效解析
    2. 实时结果显示：解析完成后立即显示 Markdown 内容
    3. 内容结构分析：展示 JSON 格式的文档结构信息
    4. 友好的用户界面：使用表情符号和格式化输出提升用户体验
    5. 自动文件管理：自动创建输出目录和组织文件

技术特点：
    - 使用命令行方式调用 MinerU，稳定性更好
    - 支持中文 OCR 和表格、公式识别
    - 轻量级实现，代码简洁易懂
    - 错误处理简单直接，便于调试

解析配置：
    - 语言：中文 (ch)
    - 后端：pipeline
    - 公式识别：启用 (true)
    - 表格识别：启用 (true)

输出特点：
    - 直接在终端显示完整的 Markdown 内容
    - 显示文档结构的详细分析
    - 提供文件统计信息
    - 显示输出目录的完整路径

目标文件：C:\Users\<USER>\Desktop\AI简历\有项目经验.pdf
输出目录：resume_parsed/

使用场景：
    - 快速查看 PDF 内容
    - 简历内容提取
    - 学习和测试 MinerU 功能
    - 日常文档处理任务

优势：
    - 代码简洁，易于理解和修改
    - 执行速度快，结果直观
    - 适合初学者和快速任务
    - 输出格式友好，便于阅读

作者：文档处理团队
创建时间：2025年
版本：1.0
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def parse_resume():
    """解析简历 PDF 文件"""
    
    # 文件路径
    pdf_path = r"C:\Users\<USER>\Desktop\AI简历\有项目经验.pdf"
    output_dir = "resume_parsed"
    
    print("🚀 开始解析简历 PDF...")
    print(f"📄 文件: {pdf_path}")
    print(f"📁 输出: {output_dir}")
    
    # 检查文件是否存在
    if not os.path.exists(pdf_path):
        print(f"❌ 错误: PDF 文件不存在 - {pdf_path}")
        return False
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 构建 MinerU 命令 - 使用当前 Python 环境
    cmd = [
        sys.executable, "-m", "mineru.cli.client",
        "-p", pdf_path,
        "-o", output_dir,
        "-l", "ch",      # 中文
        "-b", "pipeline", # 使用 pipeline 后端
        "-f", "true",    # 启用公式识别
        "-t", "true",    # 启用表格识别
    ]
    
    try:
        print("⚙️ 正在执行解析...")
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        
        print("✅ 解析完成!")
        
        # 查找生成的文件
        markdown_files = []
        json_files = []
        
        for root, dirs, files in os.walk(output_dir):
            for file in files:
                file_path = os.path.join(root, file)
                if file.endswith('.md'):
                    markdown_files.append(file_path)
                elif file.endswith('.json') and 'content_list' in file:
                    json_files.append(file_path)
        
        print(f"\n📋 生成的文件:")
        print(f"   Markdown: {len(markdown_files)} 个")
        print(f"   JSON: {len(json_files)} 个")
        
        # 显示 Markdown 内容
        if markdown_files:
            md_file = markdown_files[0]
            print(f"\n📝 Markdown 内容 ({os.path.basename(md_file)}):")
            print("-" * 50)
            
            with open(md_file, 'r', encoding='utf-8') as f:
                content = f.read()
                print(content)
        
        # 显示 JSON 结构
        if json_files:
            json_file = json_files[0]
            print(f"\n🔍 内容结构 ({os.path.basename(json_file)}):")
            print("-" * 50)
            
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                print(f"总共 {len(data)} 个内容块:")
                
                for i, item in enumerate(data):
                    item_type = item.get('type', 'unknown')
                    text = item.get('text', '').strip()
                    if text:
                        preview = text[:80] + "..." if len(text) > 80 else text
                        print(f"  {i+1:2d}. [{item_type}] {preview}")
        
        print(f"\n📂 完整输出目录: {os.path.abspath(output_dir)}")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 解析失败: {e}")
        if e.stderr:
            print(f"错误信息: {e.stderr}")
        return False
    
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("📄 MinerU 简历 PDF 解析工具")
    print("=" * 60)
    
    success = parse_resume()
    
    if success:
        print("\n🎉 解析成功完成!")
    else:
        print("\n💥 解析失败!")

if __name__ == "__main__":
    main()
