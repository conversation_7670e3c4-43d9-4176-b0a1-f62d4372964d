[{"type": "text", "text": "MinerU Test Document ", "text_level": 1, "page_idx": 0}, {"type": "text", "text": "This is a test document for MinerU PDF parsing capabilities. It contains various types of content including: ", "page_idx": 0}, {"type": "text", "text": "- Regular text paragraphs - Mathematical formulas - Tables and structured data - Mixed language content ", "page_idx": 0}, {"type": "text", "text": "Mathematical Formula Example: $\\begin{array} { l } { { \\mathsf E } = { \\mathsf m } { \\mathsf c } ^ { 2 } } \\\\ { \\int { \\mathsf f } ( { \\mathsf x } ) \\mathrm { d } { \\mathsf x } = { \\mathsf F } ( { \\mathsf x } ) + { \\mathsf C } } \\end{array}$ ", "page_idx": 0}, {"type": "text", "text": "Table Example: ", "page_idx": 0}, {"type": "text", "text": "Name Age City Alice 25 Beijing Bob 30 Shanghai Charlie 28 Guangzhou ", "page_idx": 0}, {"type": "text", "text": "This document tests the extraction capabilities of MinerU for converting PDF content to Markdown format. ", "page_idx": 0}]