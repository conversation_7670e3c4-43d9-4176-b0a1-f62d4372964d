@echo off
REM ============================================================================
REM OpenAI API 密钥环境变量设置工具
REM
REM 文件描述：
REM     这是一个用于设置 OpenAI API 密钥环境变量的批处理脚本。
REM     提供交互式界面，帮助用户安全地设置 API 密钥到系统环境变量中。
REM
REM 主要功能：
REM     1. 交互式 API 密钥输入：提供友好的用户界面输入 API 密钥
REM     2. 输入验证：检查用户是否输入了有效的 API 密钥
REM     3. 环境变量设置：使用 setx 命令设置系统级环境变量
REM     4. 操作结果反馈：显示设置成功或失败的状态信息
REM     5. 使用指导：提供后续操作的详细说明
REM
REM 技术特点：
REM     - 使用 Windows 批处理命令
REM     - 支持用户输入验证
REM     - 设置持久性环境变量（重启后仍有效）
REM     - 提供详细的操作反馈
REM
REM 安全特性：
REM     - 不在脚本中硬编码 API 密钥
REM     - 支持交互式安全输入
REM     - 设置为用户级环境变量
REM
REM 使用场景：
REM     - 初次配置 OpenAI API 环境
REM     - 更新或重置 API 密钥
REM     - 开发环境快速配置
REM     - 团队开发环境标准化
REM
REM 后续操作：
REM     设置完成后需要重新打开命令行窗口，然后可以运行：
REM     python openaiAPITEST.py
REM
REM 作者：开发环境配置团队
REM 创建时间：2025年
REM 版本：1.0
REM ============================================================================

echo OpenAI API 密钥设置工具
echo ========================
echo.
echo 请输入您的 OpenAI API 密钥：
set /p api_key=

if "%api_key%"=="" (
    echo 错误：未输入 API 密钥
    pause
    exit /b 1
)

echo.
echo 正在设置环境变量...
setx OPENAI_API_KEY "%api_key%"

if %errorlevel%==0 (
    echo ✅ API 密钥设置成功！
    echo.
    echo 请重新打开命令行窗口，然后运行：
    echo python openaiAPITEST.py
) else (
    echo ❌ 设置失败，请手动设置环境变量
)

echo.
pause
