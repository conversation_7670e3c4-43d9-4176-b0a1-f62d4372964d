# MinerU Python 解析脚本使用说明

## 📁 文件说明

我为您创建了三个不同的 Python 脚本来解析您的简历 PDF 文件：

### 1. `parse_resume_pdf.py` - 完整功能版本
- **功能**：最完整的解析脚本，包含错误处理和详细分析
- **特点**：
  - 支持多种解析方式（API + 命令行备选）
  - 详细的输出分析和预览
  - 完整的错误处理机制
  - 生成分析报告

### 2. `simple_parse.py` - 简洁版本 ⭐ 推荐
- **功能**：最简单易用的解析脚本
- **特点**：
  - 代码简洁，易于理解
  - 直接显示解析结果
  - 适合快速解析使用
  - 输出格式友好

### 3. `api_parse.py` - API 调用版本
- **功能**：直接使用 MinerU Python API
- **特点**：
  - 尝试多种 API 调用方式
  - 生成本地文件副本
  - 详细的内容结构分析
  - 生成解析报告

## 🚀 使用方法

### 方法一：运行简洁版本（推荐）

```bash
python simple_parse.py
```

### 方法二：运行完整版本

```bash
python parse_resume_pdf.py
```

### 方法三：运行 API 版本

```bash
python api_parse.py
```

## 📄 目标文件

所有脚本都会解析这个文件：
```
C:\Users\<USER>\Desktop\AI简历\有项目经验.pdf
```

## 📂 输出说明

### 输出目录
- `simple_parse.py` → `resume_parsed/`
- `parse_resume_pdf.py` → `resume_output/`
- `api_parse.py` → `resume_api_output/`

### 生成的文件类型
1. **Markdown 文件** (`.md`)
   - 包含完整的文档内容
   - 保持原有格式和结构
   - 支持数学公式和表格

2. **JSON 文件** (`.json`)
   - 结构化的内容数据
   - 包含文本类型和位置信息
   - 便于程序处理

3. **图片文件** (`.jpg`, `.png`)
   - 从 PDF 中提取的图片
   - 保持原有质量

4. **其他文件**
   - 布局分析结果
   - 模型识别数据

## 🔧 配置选项

所有脚本都使用以下配置：

```python
language = "ch"        # 中文 OCR
backend = "pipeline"   # 使用 pipeline 后端
formula = True         # 启用公式识别
table = True          # 启用表格识别
```

## 📋 示例输出

### Markdown 格式示例
```markdown
# 个人简历

## 基本信息
- 姓名：张三
- 电话：138****1234
- 邮箱：<EMAIL>

## 工作经验
### 2020-2023 | 软件工程师 | ABC公司
- 负责前端开发工作
- 使用 React、Vue.js 等技术栈
```

### JSON 格式示例
```json
[
  {
    "type": "text",
    "text": "个人简历",
    "text_level": 1,
    "page_idx": 0
  },
  {
    "type": "text", 
    "text": "基本信息",
    "page_idx": 0
  }
]
```

## ⚠️ 注意事项

1. **文件路径**：确保 PDF 文件存在于指定路径
2. **权限**：确保有读取 PDF 文件和写入输出目录的权限
3. **依赖**：确保 MinerU 已正确安装和配置
4. **内存**：大文件可能需要较多内存，建议关闭其他程序

## 🐛 故障排除

### 常见问题

1. **文件不存在错误**
   ```
   ❌ 错误: PDF 文件不存在
   ```
   **解决**：检查文件路径是否正确

2. **模块导入失败**
   ```
   导入 MinerU 模块失败
   ```
   **解决**：确保 MinerU 已正确安装

3. **解析失败**
   ```
   ❌ 解析失败: Command failed
   ```
   **解决**：检查 PDF 文件是否损坏，尝试其他脚本

### 调试方法

1. **检查 MinerU 安装**：
   ```bash
   python -c "import mineru; print('MinerU 可用')"
   ```

2. **检查文件路径**：
   ```bash
   python -c "import os; print(os.path.exists(r'C:\Users\<USER>\Desktop\AI简历\有项目经验.pdf'))"
   ```

3. **查看详细错误**：
   运行 `parse_resume_pdf.py` 获取最详细的错误信息

## 📞 获取帮助

如果遇到问题：

1. 首先尝试运行 `simple_parse.py`
2. 检查 PDF 文件是否可以正常打开
3. 确认 MinerU 环境配置正确
4. 查看生成的错误日志

## 🎯 快速开始

最简单的使用方式：

```bash
# 1. 确保在正确的目录
cd d:\code\demo

# 2. 激活虚拟环境（如果需要）
.venv\Scripts\activate

# 3. 运行解析脚本
python simple_parse.py

# 4. 查看结果
# 输出会直接显示在终端
# 文件保存在 resume_parsed/ 目录
```

解析完成后，您将得到简历的 Markdown 格式文本，可以直接复制使用或进一步编辑。
