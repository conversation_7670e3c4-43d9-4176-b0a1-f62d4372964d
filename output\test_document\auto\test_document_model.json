[{"layout_dets": [{"category_id": 1, "poly": [274, 496, 699, 496, 699, 704, 274, 704], "score": 0.966}, {"category_id": 1, "poly": [273, 1328, 1118, 1328, 1118, 1427, 273, 1427], "score": 0.941}, {"category_id": 1, "poly": [273, 1050, 689, 1050, 689, 1261, 273, 1261], "score": 0.939}, {"category_id": 0, "poly": [277, 237, 737, 237, 737, 288, 277, 288], "score": 0.916}, {"category_id": 1, "poly": [276, 997, 512, 997, 512, 1036, 276, 1036], "score": 0.853}, {"category_id": 1, "poly": [273, 383, 1166, 383, 1166, 481, 273, 481], "score": 0.549}, {"category_id": 1, "poly": [272, 774, 759, 774, 759, 928, 272, 928], "score": 0.414}, {"category_id": 14, "poly": [271, 880, 545, 880, 545, 929, 271, 929], "score": 0.49, "latex": "\\int { \\mathsf { f } } ( { \\mathsf { x } } ) \\mathrm { d } { \\mathsf { x } } = { \\mathsf { F } } ( { \\mathsf { x } } ) + { \\mathsf { C } }"}, {"category_id": 14, "poly": [270, 826, 546, 826, 546, 933, 270, 933], "score": 0.35, "latex": "\\begin{array} { l } { { \\mathsf E } = { \\mathsf m } { \\mathsf c } ^ { 2 } } \\\\ { \\int { \\mathsf f } ( { \\mathsf x } ) \\mathrm { d } { \\mathsf x } = { \\mathsf F } ( { \\mathsf x } ) + { \\mathsf C } } \\end{array}"}, {"category_id": 13, "poly": [272, 829, 398, 829, 398, 868, 272, 868], "score": 0.25, "latex": "\\mathsf { E } = \\mathsf { m c } ^ { 2 }"}, {"category_id": 15, "poly": [271.0, 231.0, 743.0, 231.0, 743.0, 293.0, 271.0, 293.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [272.0, 494.0, 659.0, 494.0, 659.0, 544.0, 272.0, 544.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [273.0, 550.0, 637.0, 550.0, 637.0, 594.0, 273.0, 594.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [272.0, 606.0, 698.0, 606.0, 698.0, 651.0, 272.0, 651.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [271.0, 661.0, 659.0, 661.0, 659.0, 710.0, 271.0, 710.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [269.0, 1049.0, 374.0, 1049.0, 374.0, 1098.0, 269.0, 1098.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [430.0, 1047.0, 511.0, 1047.0, 511.0, 1103.0, 430.0, 1103.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [524.0, 1046.0, 604.0, 1046.0, 604.0, 1105.0, 524.0, 1105.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [271.0, 1103.0, 359.0, 1103.0, 359.0, 1157.0, 271.0, 1157.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [406.0, 1104.0, 462.0, 1104.0, 462.0, 1157.0, 406.0, 1157.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [489.0, 1102.0, 608.0, 1102.0, 608.0, 1163.0, 489.0, 1163.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [270.0, 1158.0, 346.0, 1158.0, 346.0, 1210.0, 270.0, 1210.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [411.0, 1158.0, 467.0, 1158.0, 467.0, 1210.0, 411.0, 1210.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [494.0, 1159.0, 654.0, 1159.0, 654.0, 1214.0, 494.0, 1214.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [270.0, 1215.0, 393.0, 1215.0, 393.0, 1265.0, 270.0, 1265.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [420.0, 1216.0, 475.0, 1216.0, 475.0, 1265.0, 420.0, 1265.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [504.0, 1215.0, 693.0, 1215.0, 693.0, 1270.0, 504.0, 1270.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [275.0, 1330.0, 1116.0, 1330.0, 1116.0, 1371.0, 275.0, 1371.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [272.0, 1386.0, 992.0, 1386.0, 992.0, 1430.0, 272.0, 1430.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [271.0, 993.0, 515.0, 993.0, 515.0, 1042.0, 271.0, 1042.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [272.0, 380.0, 1168.0, 380.0, 1168.0, 432.0, 272.0, 432.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [271.0, 437.0, 946.0, 437.0, 946.0, 486.0, 271.0, 486.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [271.0, 770.0, 761.0, 770.0, 761.0, 818.0, 271.0, 818.0], "score": 1.0, "text": ""}], "page_info": {"page_no": 0, "width": 1700, "height": 2200}}]