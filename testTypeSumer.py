#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
软件测试覆盖度分析工具

文件描述：
    这是一个专门用于分析软件测试文档中测试覆盖度的统计工具。
    通过解析 Word 文档中的测试表格，统计各种测试类型的覆盖情况。

主要功能：
    1. 测试表格解析：自动识别和解析文档中的所有测试表格
    2. 测试对象提取：从测试ID中提取测试对象名称进行分类
    3. 测试类型统计：统计功能测试、接口测试、性能测试等各类型的覆盖度
    4. 多维度分析：按测试对象和测试类型进行交叉统计
    5. 报告生成：生成详细的测试覆盖度分析报告

技术特点：
    - 使用 python-docx 解析 Word 文档
    - 使用 pandas 进行数据分析和报表生成
    - 使用 defaultdict 进行高效的数据统计
    - 支持复杂的表格结构解析

测试类型支持：
    - 功能测试（列索引：5）
    - 接口测试（列索引：6）
    - 性能测试（列索引：7）
    - 人机界面测试（列索引：8）
    - 兼容性测试（列索引：9）

统计逻辑：
    - 识别表格中的对号标记（✓ 或 √）
    - 按测试对象分组统计
    - 生成按表格和总体的统计报告
    - 提供详细的数据分析结果

输出报告：
    - 按测试对象和类别的对号统计表
    - 每个表格的测试类型统计
    - 各测试类型的总计数量
    - 完整的测试覆盖度分析

输入文件：软件测试计划z.docx

使用场景：
    - 软件测试计划评估
    - 测试覆盖度质量分析
    - 测试工作量统计
    - 测试完整性检查

作者：测试质量团队
创建时间：2025年
版本：1.0
"""

from docx import Document
import pandas as pd
from collections import defaultdict

def extract_test_object(test_id):
    """
    从测试ID中提取测试对象名称
    示例: CKGHZXT_GN_STJXZY_FY_TZDSB -> CKGHZXT_GN_STJXZY
    """
    try:
        return '_'.join(test_id.split('_')[:3])
    except:
        return "未知测试对象"

def analyze_test_tables(doc_path):
    """
    分析Word文档中的所有测试表格并按类别和测试对象统计对号
    """
    doc = Document(doc_path)
    
    # 初始化结果存储结构
    results = defaultdict(lambda: defaultdict(int))
    test_counts = defaultdict(lambda: defaultdict(int))
    
    # 测试类型列映射
    test_type_columns = {
        '功能测试': 5,
        '接口测试': 6,
        '性能测试': 7,
        '人机界面测试': 8,
        '兼容性测试': 9
    }
    
    # 遍历文档中的所有表格
    table_count = 0
    for table in doc.tables:
        table_count += 1
        print(f"正在处理第 {table_count} 个表格...")
        
        # 跳过空表格或格式不正确的表格
        if len(table.rows) < 2:
            print(f"表格 {table_count} 为空或格式不正确，已跳过")
            continue
        
        table_test_counts = {test_type: 0 for test_type in test_type_columns.keys()}  # Initialize count for each test type in the current table
        
        for row_idx, row in enumerate(table.rows[1:], 1):  # 跳过表头行
            cells = row.cells
            
            # 跳过空行
            if len(cells) < 2 or not cells[1].text.strip():
                continue
                
            # 从测试ID提取测试对象
            test_id = cells[1].text.strip()
            test_object = extract_test_object(test_id)
            
            # 统计每种测试类型的对号
            for test_type, col_idx in test_type_columns.items():
                if col_idx < len(cells) and ('✓' in cells[col_idx].text or '√' in cells[col_idx].text):
                    results[test_object][test_type] += 1
                    table_test_counts[test_type] += 1
        
        test_counts[table_count] = table_test_counts  # Store counts for the current table
    
    return results, table_count, test_counts

def generate_summary_report(results, test_counts):
    """
    生成分析结果的汇总报告
    """
    # 转换结果为DataFrame便于报告
    df = pd.DataFrame.from_dict(results, orient='index')
    
    # 将NaN值填充为0
    df = df.fillna(0)
    
    # 添加合计行
    df.loc['总计'] = df.sum()
    
    # Add table-wise counts for each test type
    test_count_df = pd.DataFrame(test_counts).T
    
    return df, test_count_df

def main(doc_path):
    """
    主函数：处理文档并生成报告
    """
    try:
        print("开始分析文档...")
        results, table_count, test_counts = analyze_test_tables(doc_path)
        
        print(f"\n共处理了 {table_count} 个表格")
        
        # 生成汇总报告
        report, table_test_counts = generate_summary_report(results, test_counts)
        
        print("\n=== 测试覆盖度分析报告 ===")
        print("\n按测试对象和类别的对号统计:")
        print(report.to_string())
        
        print("\n每个表格的测试类型统计:")
        print(table_test_counts.to_string())
        
        # 打印每种测试类型的总计
        print("\n各测试类型的总对号数:")
        for test_type in report.columns:
            print(f"{test_type}: {report.loc['总计', test_type]:.0f}")
            
    except Exception as e:
        print(f"处理文档时出错: {str(e)}")

# 使用示例
if __name__ == "__main__":
    doc_path = "软件测试计划z.docx"
    main(doc_path)
