# MinerU Test Document

This is a test document for MinerU PDF parsing capabilities. It contains various types of content including:

- Regular text paragraphs - Mathematical formulas - Tables and structured data - Mixed language content

Mathematical Formula Example: $\begin{array} { l } { { \mathsf E } = { \mathsf m } { \mathsf c } ^ { 2 } } \\ { \int { \mathsf f } ( { \mathsf x } ) \mathrm { d } { \mathsf x } = { \mathsf F } ( { \mathsf x } ) + { \mathsf C } } \end{array}$

Table Example:

Name Age City Alice 25 Beijing Bob 30 Shanghai Charlie 28 Guangzhou

This document tests the extraction capabilities of MinerU for converting PDF content to Markdown format.